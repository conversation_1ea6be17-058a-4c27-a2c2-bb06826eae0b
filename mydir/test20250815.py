#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KISS/YAGNI 版本：忠实折线 + SLERP + Euler 连续展开 + 弧长时间标定(线/角/曲率限速)
- 去掉复杂日志与多余开关，只保留关键输出（开始/模式切换/速度切换/统计/完成/错误）。
- 保持功能与默认参数一致，可一键运行。
- 结构化/分层设计仍在（SOLID：清晰职责），但实现尽量直接。

使用示例：
  python runner_simple.py -f your.gcode
"""
from __future__ import annotations
import os, re, time, math, threading, argparse, sys, datetime
from queue import Queue, Empty

# ====== 默认参数 ======
GCODE_FILE = "jiyi copy 2.Gcode"
try:
    from config import ROBOT_IP, ROBOT_PORT  # 可选配置模块
except Exception:
    ROBOT_IP, ROBOT_PORT = "************", "6001"

USER_COORD_NUMBER = 1
G0_VEL_PCT = 50
G1_PCT_MIN = 45
G1_PCT_MAX = 60
G1_PCT_STEP = 5
ACCEL_PCT = 20
PL_SMOOTH  = 5

BATCH_SEND_DEFAULT = 5
Q_HIGH_DEFAULT     = 20
Q_LOW_DEFAULT      = 5

# 采样/时间
GABC_TO_RXYZ = (180.0, 0.0, 0.0)  # A=B=C=0 -> RX=180,RY=0,RZ=0
DS_POS = 10.0
DA_DEG = 2.0
V_LIN_MAX = 60.0
V_ANG_MAX = 45.0
A_EQ_MAX  = 300.0
A_LAT_MAX = 800.0
V_END_RATIO = 0.05
EPS_POS = 1e-4
EPS_ANG = 1e-3

# ====== 轻量输出 ======

def now_ts():
    return datetime.datetime.now().strftime("%H:%M:%S")

def info(msg: str):
    print(f"[{now_ts()}] {msg}")

def warn(msg: str):
    print(f"[{now_ts()}] [WARN] {msg}")

def err(msg: str):
    print(f"[{now_ts()}] [ERR] {msg}")

# ====== 数学/姿态 ======

def deg2rad(d): return d * math.pi / 180.0

def rad2deg(r): return r * 180.0 / math.pi

def clamp(x, lo, hi): return max(lo, min(hi, x))

def normalize_angle_deg(a):
    a = (a + 180.0) % 360.0 - 180.0
    if a <= -180.0: a += 360.0
    return a

def unwrap_to_prev(cur, prev):  # 让cur加/减360后尽量靠近prev
    return cur + 360.0 * round((prev - cur) / 360.0)

def quat_normalize(q):
    w,x,y,z = q
    n = math.sqrt(w*w+x*x+y*y+z*z)
    return (1.0,0.0,0.0,0.0) if n==0 else (w/n,x/n,y/n,z/n)

def quat_dot(a,b): return a[0]*b[0]+a[1]*b[1]+a[2]*b[2]+a[3]*b[3]

def euler_xyz_deg_to_quat(rx, ry, rz):
    rx,ry,rz = deg2rad(rx),deg2rad(ry),deg2rad(rz)
    cx,sx = math.cos(rx/2), math.sin(rx/2)
    cy,sy = math.cos(ry/2), math.sin(ry/2)
    cz,sz = math.cos(rz/2), math.sin(rz/2)
    w = cz*cy*cx + sz*sy*sx
    x = cz*cy*sx - sz*sy*cx
    y = cz*sy*cx + sz*cy*sx
    z = sz*cy*cx - cz*sy*sx
    return quat_normalize((w,x,y,z))

def quat_to_euler_xyz_deg(q):
    # 修复：对 asin 的入参进行 clamp（防数值飘）
    w,x,y,z = q
    r11 = 1 - 2*(y*y + z*z)
    r21 = 2*(x*y + w*z)
    r31 = 2*(x*z - w*y)
    r32 = 2*(y*z + w*x)
    r33 = 1 - 2*(x*x + y*y)
    rx = math.atan2(r32, r33)
    ry = -math.asin(clamp(r31, -1.0, 1.0))
    rz = math.atan2(r21, r11)
    return (rad2deg(rx), rad2deg(ry), rad2deg(rz))

def quat_slerp(q0, q1, t):
    c = quat_dot(q0,q1)
    if c < 0.0:
        q1 = (-q1[0],-q1[1],-q1[2],-q1[3]); c = -c
    if c > 0.9995:  # 线性近似
        w = q0[0] + t*(q1[0]-q0[0]); x = q0[1] + t*(q1[1]-q0[1])
        y = q0[2] + t*(q1[2]-q0[2]); z = q0[3] + t*(q1[3]-q0[3])
        return quat_normalize((w,x,y,z))
    th = math.acos(clamp(c,-1.0,1.0))
    s = math.sin(th)
    s0 = math.sin((1-t)*th) / s
    s1 = math.sin(t*th) / s
    return (q0[0]*s0+q1[0]*s1, q0[1]*s0+q1[1]*s1, q0[2]*s0+q1[2]*s1, q0[3]*s0+q1[3]*s1)

def chord_len(p,q):
    dx,dy,dz = (q[0]-p[0], q[1]-p[1], q[2]-p[2])
    return math.sqrt(dx*dx+dy*dy+dz*dz)

def angle_between_quats_deg(q0,q1):
    c = clamp(abs(quat_dot(q0,q1)),-1.0,1.0)
    return math.degrees(2.0*math.acos(c))

def discrete_curvature(p_prev, p_cur, p_next):
    # 修复：v2.z 应为 (p_next[2]-p_cur[2])
    v1 = (p_cur[0]-p_prev[0], p_cur[1]-p_prev[1], p_cur[2]-p_prev[2])
    v2 = (p_next[0]-p_cur[0], p_next[1]-p_cur[1], p_next[2]-p_cur[2])
    l1 = math.sqrt(v1[0]**2+v1[1]**2+v1[2]**2) + 1e-9
    l2 = math.sqrt(v2[0]**2+v2[1]**2+v2[2]**2) + 1e-9
    dot = clamp((v1[0]*v2[0]+v1[1]*v2[1]+v1[2]*v2[2])/(l1*l2), -1.0, 1.0)
    theta = math.acos(dot)
    s = 0.5*(l1+l2)
    return 0.0 if s < 1e-9 else theta/s

# ====== SDK 封装 ======
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc


def rc_unpack(res):
    """返回 (ok, data, code)。统一解包 SDK 返回值。"""
    if isinstance(res, (list, tuple)) and len(res) >= 1:
        code = res[0]
        data = res[1] if len(res) > 1 else None
        return (code == 0), data, code
    return False, None, None


class RobotController:
    """SOLID: 仅负责控制器相关操作。"""
    def __init__(self, ip, port):
        self.ip, self.port = ip, port
        self.fd = -1
        self.connected = False

    def connect(self):
        info(f"连接 {self.ip}:{self.port} ...")
        try:
            from contextlib import redirect_stdout, redirect_stderr
            with open(os.devnull, "w") as devnull, redirect_stdout(devnull), redirect_stderr(devnull):
                self.fd = nrc.connect_robot(self.ip, str(self.port))
        except Exception:
            self.fd = nrc.connect_robot(self.ip, str(self.port))
        self.connected = self.fd > 0
        info("已连接" if self.connected else "连接失败")
        return self.connected

    def disconnect(self):
        if self.connected:
            try:
                from contextlib import redirect_stdout, redirect_stderr
                with open(os.devnull, "w") as devnull, redirect_stdout(devnull), redirect_stderr(devnull):
                    nrc.disconnect_robot(self.fd)
            finally:
                self.connected, self.fd = False, -1

    def _power_on_if_needed(self):
        try:
            ok, cur, _ = rc_unpack(nrc.get_servo_state(self.fd, 0))
            if ok and cur == 3:
                return True
            nrc.set_current_mode(self.fd, 0); time.sleep(0.1)
            nrc.clear_error(self.fd)
            nrc.set_servo_state(self.fd, 1); time.sleep(1.0)
            if nrc.set_servo_poweron(self.fd) != 0:
                return False
            time.sleep(1.0)
            ok, cur, _ = rc_unpack(nrc.get_servo_state(self.fd, 0))
            return ok and cur == 3
        except Exception:
            return False

    def initialize(self):
        if not self.connected or not self._power_on_if_needed():
            return False
        if nrc.set_current_mode(self.fd, 2) != 0:
            err("切到运行模式失败"); return False
        nrc.queue_motion_clear_Data(self.fd)
        if nrc.queue_motion_set_status(self.fd, True) != 0:
            err("启用队列失败"); return False
        if nrc.set_user_coord_number(self.fd, USER_COORD_NUMBER) != 0:
            err("设置用户坐标失败"); return False
        info("初始化完毕（运行模式/队列开启）")
        return True

    def power_off(self):
        try:
            nrc.queue_motion_stop(self.fd); time.sleep(0.2); nrc.set_servo_poweroff(self.fd)
        except Exception:
            pass

    def get_queue_length(self):
        ok, data, _ = rc_unpack(nrc.queue_motion_get_queuelen(self.fd, 0))
        return data if ok else -1

    def set_speed(self, percent):
        ok = (nrc.set_speed(self.fd, percent) == 0)
        return ok

    def add_point(self, x,y,z, rx_deg, ry_deg, rz_deg, move_type='G1'):
        rx,ry,rz = deg2rad(rx_deg),deg2rad(ry_deg),deg2rad(rz_deg)
        cmd = nrc.MoveCmd()
        cmd.targetPosType = 0
        cmd.targetPosValue = nrc.VectorDouble([x,y,z, rx,ry,rz])
        cmd.coord = 3; cmd.userNum = USER_COORD_NUMBER
        cmd.velocity = 100; cmd.acc = ACCEL_PCT; cmd.dec = ACCEL_PCT; cmd.pl = PL_SMOOTH
        r = nrc.queue_motion_push_back_moveL(self.fd, cmd) if move_type=='G1' \
            else nrc.queue_motion_push_back_moveJ(self.fd, cmd)
        if r != 0:
            raise RuntimeError(f"push_back失败: {r}")

    def flush(self, n):
        if n <= 0: return True
        return (nrc.queue_motion_send_to_controller(self.fd, n) == 0)

# ====== G-code 解析 ======
_COORD_RE = re.compile(r'([XYZABCFE])([-+]?\d*\.?\d+)')
class WP:
    __slots__ = ("cmd","x","y","z","a","b","c","q")
    def __init__(self, cmd, x,y,z, a,b,c, q):
        self.cmd, self.x, self.y, self.z = cmd, x,y,z
        self.a, self.b, self.c = a,b,c
        self.q = q

def parse_line(line: str):
    s = line.strip()
    if not s or s.startswith(';'): return None
    up = s.upper()
    if not (up.startswith('G0') or up.startswith('G1')): return None
    cmd = 'G0' if up.startswith('G0') else 'G1'
    vals = dict((k, float(v)) for k,v in _COORD_RE.findall(up))
    x = vals.get('X'); y = vals.get('Y'); z = vals.get('Z')
    a = vals.get('A'); b = vals.get('B'); c = vals.get('C')
    return cmd, x,y,z, a,b,c

def gabc_to_rxyz(a,b,c):
    rx = normalize_angle_deg(a + GABC_TO_RXYZ[0])
    ry = normalize_angle_deg(b + GABC_TO_RXYZ[1])
    rz = normalize_angle_deg(c + GABC_TO_RXYZ[2])
    return rx,ry,rz

def load_wps(filepath):
    last = {'x':0.0,'y':0.0,'z':20.0,'a':0.0,'b':0.0,'c':0.0}
    wps = []
    with open(filepath,'r',encoding='utf-8') as f:
        for raw in f:
            p = parse_line(raw)
            if not p: continue
            cmd,x,y,z,a,b,c = p
            cur = dict(last)
            if x is not None: cur['x']=x
            if y is not None: cur['y']=y
            if z is not None: cur['z']=z
            if cmd == 'G0':
                cur['a']=0.0; cur['b']=0.0; cur['c']=0.0
            else:
                if a is not None: cur['a']=a
                if b is not None: cur['b']=b
                if c is not None: cur['c']=c
            rx,ry,rz = gabc_to_rxyz(cur['a'],cur['b'],cur['c'])
            q = euler_xyz_deg_to_quat(rx,ry,rz)
            wps.append(WP(cmd, cur['x'],cur['y'],cur['z'], cur['a'],cur['b'],cur['c'], q))
            last = cur
    # 半球对齐
    for i in range(1,len(wps)):
        if quat_dot(wps[i-1].q, wps[i].q) < 0.0:
            w = wps[i]; wps[i].q = (-w.q[0],-w.q[1],-w.q[2],-w.q[3])
    return wps

# ====== 细分 + 姿态 ======

def polyline_presample_block(P, Q):
    out = [(P[0][0],P[0][1],P[0][2], Q[0])]
    for i in range(len(P)-1):
        p0,p1 = P[i],P[i+1]; q0,q1 = Q[i],Q[i+1]
        L = chord_len(p0,p1); ang = angle_between_quats_deg(q0,q1)
        n_pos = max(1, int(math.ceil(L / DS_POS)))
        n_ang = max(1, int(math.ceil(ang / DA_DEG)))
        steps = max(n_pos, n_ang)
        for k in range(1, steps+1):
            t = k/steps
            x = p0[0] + (p1[0]-p0[0]) * t
            y = p0[1] + (p1[1]-p0[1]) * t
            z = p0[2] + (p1[2]-p0[2]) * t
            q = quat_slerp(q0,q1,t)
            out.append((x,y,z,q))
    return out


def poses_quat_to_unwrapped_deg(poses_xyz_q):
    out=[]; last_rx=last_ry=last_rz=None; last_x=last_y=last_z=None
    for x,y,z,q in poses_xyz_q:
        rx,ry,rz = quat_to_euler_xyz_deg(q)
        if last_rx is None: rxu,ryu,rzu = rx,ry,rz
        else:
            rxu = unwrap_to_prev(rx, last_rx)
            ryu = unwrap_to_prev(ry, last_ry)
            rzu = unwrap_to_prev(rz, last_rz)
        if last_x is not None:
            dp = math.sqrt((x-last_x)**2 + (y-last_y)**2 + (z-last_z)**2)
            da = max(abs(rxu-last_rx), abs(ryu-last_ry), abs(rzu-last_rz))
            if dp < EPS_POS and da < EPS_ANG: continue
        out.append((x,y,z, rxu,ryu,rzu))
        last_rx, last_ry, last_rz = rxu,ryu,rzu
        last_x,  last_y,  last_z  = x,y,z
    return out

# ====== 时间标定（曲率/角速度/线速度联合限速） ======

def time_scale_polyline(P, Q):
    n=len(P)
    if n<2: return [0.0],[G1_PCT_MIN],[(0,0,G1_PCT_MIN)]
    L  = [chord_len(P[i],P[i+1]) for i in range(n-1)]
    TH = [angle_between_quats_deg(Q[i],Q[i+1]) for i in range(n-1)]
    kappa=[0.0]*n
    for i in range(1,n-1):
        kappa[i]=discrete_curvature(P[i-1],P[i],P[i+1])
    v_curve=[]
    for i in range(n-1):
        k=max(kappa[i],kappa[i+1])
        v_curve.append(math.sqrt(A_LAT_MAX/max(k,1e-9)) if k>1e-9 else float('inf'))
    v_local=[]
    for i in range(n-1):
        v_lin=V_LIN_MAX
        v_ang=V_ANG_MAX*(L[i]/max(TH[i],1e-6))
        v_local.append(min(v_lin,v_ang,v_curve[i]))
    v_nodes=[0.0]*n
    vmax_all=max(v_local) if v_local else 0.0
    v_nodes[0]=V_END_RATIO*vmax_all
    for i in range(1,n):
        v_allow=math.sqrt(max(0.0, v_nodes[i-1]*v_nodes[i-1]+2*A_EQ_MAX*L[i-1]))
        v_nodes[i]=min(v_allow, v_local[i-1])
    v_nodes[-1]=min(v_nodes[-1], V_END_RATIO*vmax_all)
    for i in range(n-2,-1,-1):
        v_allow=math.sqrt(max(0.0, v_nodes[i+1]*v_nodes[i+1]+2*A_EQ_MAX*L[i]))
        v_nodes[i]=min(v_nodes[i], v_allow, v_local[i])
    v_seg=[min(v_nodes[i],v_nodes[i+1],v_local[i]) for i in range(n-1)]
    v_peak=max(v_seg) if v_seg else 1.0
    if v_peak<1e-9: v_peak=1.0
    p_seg=[]
    for v in v_seg:
        r=v/v_peak
        p=G1_PCT_MIN + r * (G1_PCT_MAX - G1_PCT_MIN)
        p=int(round(p / G1_PCT_STEP) * G1_PCT_STEP)
        p=clamp(p,G1_PCT_MIN,G1_PCT_MAX); p_seg.append(p)
    merged=[]; start=0; cur=p_seg[0]
    for i in range(len(p_seg)):
        if p_seg[i]!=cur:
            merged.append((start,i,cur)); start=i; cur=p_seg[i]
    merged.append((start,len(p_seg),cur))
    return L,p_seg,merged

# ====== 生产者（读取/细分/时间标定 -> 两条队列） ======

def producer(filepath: str, q_pose: Queue, q_sched: Queue, stop: threading.Event):
    try:
        info("读取与预处理开始…")
        wps = load_wps(filepath)
        if not wps:
            warn("无有效 G-code，直接结束")
            return
        i=0
        while i<len(wps):
            while i<len(wps) and wps[i].cmd=='G0':
                rx,ry,rz = gabc_to_rxyz(0.0,0.0,0.0)
                q_pose.put(('G0', (wps[i].x,wps[i].y,wps[i].z, rx,ry,rz))); i+=1
            if i>=len(wps): break
            j=i
            while j<len(wps) and wps[j].cmd=='G1': j+=1
            block=wps[i:j]
            P=[(w.x,w.y,w.z) for w in block]
            Q=[w.q for w in block]
            for k in range(1,len(Q)):
                if quat_dot(Q[k-1],Q[k])<0.0:
                    wq=Q[k]; Q[k]=(-wq[0],-wq[1],-wq[2],-wq[3])
            fine = polyline_presample_block(P,Q)
            fine_deg = poses_quat_to_unwrapped_deg(fine)
            _,_,merged = time_scale_polyline(P,Q)
            q_pose.put(('G1', fine_deg[0]))
            # 段->采样点前缀和
            def ang_deg(q0,q1):
                return math.degrees(2.0*math.acos(clamp(abs(quat_dot(q0,q1)),-1.0,1.0)))
            seg_steps=[]; pref=[0]
            for s in range(len(P)-1):
                Ls=chord_len(P[s],P[s+1]); THs=ang_deg(Q[s],Q[s+1])
                n_pos=max(1,int(math.ceil(Ls/DS_POS)))
                n_ang=max(1,int(math.ceil(THs/DA_DEG)))
                st=max(n_pos,n_ang); seg_steps.append(st); pref.append(pref[-1]+st)
            base_idx=1
            for seg_l,seg_r,pct in merged:
                send_steps=pref[seg_r]-pref[seg_l]
                end_idx=base_idx+send_steps
                for k in range(base_idx,end_idx+1):
                    if k<len(fine_deg): q_pose.put(('G1', fine_deg[k]))
                q_sched.put(('G1', pct))
                base_idx=end_idx+1
            i=j
        info("读取与预处理完成")
    except Exception as e:
        err(f"producer 异常: {e}")
        try: q_pose.put(('ERR', str(e)))
        except Exception: pass
    finally:
        try: q_pose.put(None)
        except Exception: pass
        try: q_sched.put(None)
        except Exception: pass


# ====== 主流程 ======

class RC(RobotController):
    pass


def main():
    global DS_POS, DA_DEG, V_LIN_MAX, V_ANG_MAX, A_EQ_MAX, A_LAT_MAX, PL_SMOOTH
    global G1_PCT_MIN, G1_PCT_MAX, G1_PCT_STEP

    pa = argparse.ArgumentParser("Polyline+SLERP+TimeScale (simple)")
    pa.add_argument("-f","--file", default=GCODE_FILE)
    # 采样/速度参数（够用即可）
    pa.add_argument("--ds", type=float, default=DS_POS)
    pa.add_argument("--da", type=float, default=DA_DEG)
    pa.add_argument("--vlin", type=float, default=V_LIN_MAX)
    pa.add_argument("--vang", type=float, default=V_ANG_MAX)
    pa.add_argument("--aeq",  type=float, default=A_EQ_MAX)
    pa.add_argument("--alat", type=float, default=A_LAT_MAX)
    pa.add_argument("--pl", type=int, default=PL_SMOOTH)
    pa.add_argument("--g1min", type=int, default=G1_PCT_MIN)
    pa.add_argument("--g1max", type=int, default=G1_PCT_MAX)
    pa.add_argument("--g1step", type=int, default=G1_PCT_STEP)
    # 队列参数
    pa.add_argument("--batch-send", type=int, default=BATCH_SEND_DEFAULT)
    pa.add_argument("--q-high", type=int, default=Q_HIGH_DEFAULT)
    pa.add_argument("--q-low",  type=int, default=Q_LOW_DEFAULT)
    # 收尾
    pa.add_argument("--idle-exit", type=float, default=3.0, help="空闲这么久仍无新点且队列为0则收尾(s)")
    pa.add_argument("--stats-every", type=float, default=1.0, help="统计输出间隔(s)")

    args = pa.parse_args()

    # 覆盖参数
    DS_POS,DA_DEG = args.ds,args.da
    V_LIN_MAX,V_ANG_MAX,A_EQ_MAX,A_LAT_MAX = args.vlin,args.vang,args.aeq,args.alat
    PL_SMOOTH = args.pl
    G1_PCT_MIN,G1_PCT_MAX,G1_PCT_STEP = args.g1min,args.g1max,args.g1step

    BATCH_SEND = max(1, int(args.batch_send))
    Q_HIGH     = max(1, int(args.q_high))
    Q_LOW      = max(0, int(args.q_low))
    if Q_LOW >= Q_HIGH: Q_LOW = max(0, Q_HIGH-1)

    q_pose: Queue = Queue(maxsize=12000)
    q_sched: Queue = Queue(maxsize=200)
    stop = threading.Event()

    rc = RC(ROBOT_IP, ROBOT_PORT)

    # 门控（≥高水位暂停；≤低水位恢复）
    def should_pause(length):  return length >= Q_HIGH
    def should_resume(length): return length <= Q_LOW

    try:
        if not rc.connect() or not rc.initialize():
            return
        t0=time.time()
        rc.set_speed(G0_VEL_PCT)
        rx0,ry0,rz0 = gabc_to_rxyz(0.0,0.0,0.0)
        rc.add_point(0.0,0.0,20.0, rx0,ry0,rz0, move_type='G0'); rc.flush(1)

        prod = threading.Thread(target=producer, args=(args.file, q_pose, q_sched, stop), daemon=True)
        prod.start()

        sent=0; batch=[]; cur_mode='G0'; cur_pct=None
        paused=False; eof_pose=False
        last_stats_t = 0.0
        last_sent_change_t = time.time()

        info(f"开始执行：batch={BATCH_SEND}, Q_HIGH={Q_HIGH}, Q_LOW={Q_LOW}")

        drain_zero_count = 0

        while not stop.is_set():
            ctl_len = rc.get_queue_length()
            if ctl_len < 0:
                err("队列查询失败"); break

            # 门控
            new_paused = paused
            if should_pause(ctl_len): new_paused = True
            elif should_resume(ctl_len): new_paused = False
            if new_paused != paused:
                paused = new_paused
                info(f"门控 -> {'暂停' if paused else '恢复'} | 队列={ctl_len}")

            # 速度档位（非阻塞）
            try:
                drained = 0
                pending_pct = None
                while True:
                    it = q_sched.get_nowait()
                    if it is None:
                        break
                    _, pct = it
                    drained += 1
                    pending_pct = pct
                if pending_pct is not None and pending_pct != cur_pct:
                    if batch: rc.flush(len(batch)); batch.clear()
                    rc.set_speed(pending_pct); cur_pct = pending_pct
                    info(f"速度 -> {cur_pct}% (合并{drained}段)")
            except Empty:
                pass

            # 取点&发送
            if not paused:
                try:
                    it = q_pose.get(timeout=0.05)
                except Empty:
                    it = "TIMEOUT"

                if it is None:
                    eof_pose = True
                    if batch:
                        rc.flush(len(batch)); batch.clear()
                elif it == "TIMEOUT":
                    pass
                elif isinstance(it, tuple) and it and it[0] == 'ERR':
                    err(f"PRODUCER ERROR -> {it[1]}")
                    eof_pose = True
                else:
                    move_type, pose = it
                    if move_type != cur_mode:
                        if batch: rc.flush(len(batch)); batch.clear()
                        rc.set_speed(G0_VEL_PCT if move_type=='G0' else (cur_pct or G1_PCT_MIN))
                        cur_mode = move_type
                        info(f"模式 -> {cur_mode}")
                    x,y,z,rx,ry,rz = pose
                    rc.add_point(x,y,z, rx,ry,rz, move_type)
                    batch.append(it); sent += 1
                    last_sent_change_t = time.time()
                    if len(batch) >= BATCH_SEND:
                        rc.flush(len(batch)); batch.clear()
            else:
                if batch:
                    rc.flush(len(batch)); batch.clear()
                time.sleep(0.01)

            # ======= 收尾判定 =======
            done = False; reason = ""

            if eof_pose and len(batch) == 0:
                if ctl_len == 0:
                    drain_zero_count += 1
                    if drain_zero_count >= 2:
                        done = True; reason = "EOF+队列清零"
                else:
                    drain_zero_count = 0

            if not done and ctl_len==0 and (time.time()-last_sent_change_t) >= args.idle_exit:
                done = True; reason = f"空闲超时 {args.idle_exit}s"

            if done:
                dt = time.time()-t0
                info(f"完成 | 点数:{sent} | 用时:{dt:.2f}s | 收尾:{reason}")
                break

            # 简洁状态
            now = time.time()
            if now - last_stats_t >= args.stats_every:
                speed_display = (G0_VEL_PCT if cur_mode == 'G0' else (cur_pct or 0))
                info(f"sent={sent} | q={ctl_len} | batch={BATCH_SEND} | gate={'停' if paused else '走'} | speed={speed_display}% | mode={cur_mode}")
                last_stats_t = now

    except KeyboardInterrupt:
        warn("中断")
    except Exception as e:
        err(f"异常：{e}")
    finally:
        stop.set()
        try: rc.power_off()
        finally: rc.disconnect()


if __name__ == "__main__":
    main()
