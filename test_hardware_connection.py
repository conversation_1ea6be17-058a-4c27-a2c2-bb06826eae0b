#!/usr/bin/env python3
"""
NRC机械臂硬件连接测试
测试与实际机械臂的连接和基础控制功能
"""

import sys
import os
import time

# 添加Klipper路径
klippy_path = os.path.join(os.path.dirname(__file__), 'klippy')
sys.path.insert(0, klippy_path)
sys.path.insert(0, os.path.join(klippy_path, 'extras'))

# 添加NRC SDK路径（使用正确的路径）
nrc_lib_path = os.path.join(os.path.dirname(__file__), 'lib')
if nrc_lib_path not in sys.path:
    sys.path.append(nrc_lib_path)

def test_nrc_sdk_import():
    """测试NRC SDK导入"""
    print("=== 测试NRC SDK导入 ===")
    
    try:
        from inexbot import nrc_interface as nrc
        print("✅ NRC SDK导入成功")
        
        # 检查关键函数是否存在
        required_functions = [
            'connect_robot',
            'disconnect_robot', 
            'get_servo_state',
            'set_servo_poweron',
            'set_current_mode',
            'queue_motion_clear_Data',
            'queue_motion_set_status',
            'set_user_coord_number',
            'queue_motion_get_queuelen',
            'get_robot_running_state'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if hasattr(nrc, func_name):
                print(f"  ✅ {func_name}")
            else:
                print(f"  ❌ {func_name}")
                missing_functions.append(func_name)
        
        if missing_functions:
            print(f"❌ 缺少函数: {missing_functions}")
            return False
        else:
            print("✅ 所有必需函数都存在")
            return nrc
            
    except ImportError as e:
        print(f"❌ NRC SDK导入失败: {e}")
        print("请检查以下路径是否包含nrc_interface.py:")
        for path in nrc_lib_paths:
            nrc_file = os.path.join(path, 'nrc_interface.py')
            exists = "✅" if os.path.exists(nrc_file) else "❌"
            print(f"  {exists} {nrc_file}")
        return False

def test_robot_connection(robot_ip="************", robot_port="6001"):
    """测试机械臂连接"""
    print(f"\n=== 测试机械臂连接 ({robot_ip}:{robot_port}) ===")

    # 导入NRC SDK
    try:
        from inexbot import nrc_interface as nrc
    except ImportError as e:
        print(f"❌ NRC SDK导入失败: {e}")
        return False
    
    try:
        print(f"正在连接到机械臂 {robot_ip}:{robot_port}...")
        
        # 尝试连接（使用你的连接逻辑）
        try:
            from contextlib import redirect_stdout, redirect_stderr
            with open(os.devnull, "w") as devnull, redirect_stdout(devnull), redirect_stderr(devnull):
                fd = nrc.connect_robot(robot_ip, robot_port)
        except Exception:
            fd = nrc.connect_robot(robot_ip, robot_port)
        
        if fd <= 0:
            print(f"❌ 连接失败，返回值: {fd}")
            print("请检查:")
            print("  1. 机械臂IP地址是否正确")
            print("  2. 机械臂是否开机")
            print("  3. 网络连接是否正常")
            print("  4. 防火墙设置")
            return False

        print(f"✅ 连接成功，文件描述符: {fd}")
        return (fd, nrc)  # 返回连接句柄和nrc模块
        
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def rc_unpack(res):
    """解包SDK返回值（来自你的test20250815.py）"""
    if isinstance(res, (list, tuple)) and len(res) >= 1:
        code = res[0]
        data = res[1] if len(res) > 1 else None
        return (code == 0), data, code
    return False, None, None

def test_robot_status(fd, nrc):
    """测试机械臂状态查询"""
    print(f"\n=== 测试机械臂状态查询 ===")
    
    try:
        # 获取伺服状态
        print("查询伺服状态...")
        ok, servo_state, code = rc_unpack(nrc.get_servo_state(fd, 0))
        if ok:
            servo_states = {
                0: "未知状态",
                1: "就绪状态", 
                2: "停止状态",
                3: "运行状态",
                4: "错误状态"
            }
            state_name = servo_states.get(servo_state, f"未知({servo_state})")
            print(f"  ✅ 伺服状态: {servo_state} ({state_name})")
        else:
            print(f"  ❌ 伺服状态查询失败，错误码: {code}")
        
        # 获取运行状态
        print("查询运行状态...")
        ok, running_state, code = rc_unpack(nrc.get_robot_running_state(fd, 0))
        if ok:
            running_states = {
                0: "停止",
                1: "运行", 
                2: "暂停",
                3: "错误"
            }
            state_name = running_states.get(running_state, f"未知({running_state})")
            print(f"  ✅ 运行状态: {running_state} ({state_name})")
        else:
            print(f"  ❌ 运行状态查询失败，错误码: {code}")
        
        # 获取队列长度
        print("查询队列长度...")
        ok, queue_len, code = rc_unpack(nrc.queue_motion_get_queuelen(fd, 0))
        if ok:
            print(f"  ✅ 队列长度: {queue_len}")
        else:
            print(f"  ❌ 队列长度查询失败，错误码: {code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 状态查询异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_robot_initialization(fd, nrc):
    """测试机械臂初始化（来自你的test20250815.py逻辑）"""
    print(f"\n=== 测试机械臂初始化 ===")
    
    try:
        # 检查当前伺服状态
        print("检查当前伺服状态...")
        ok, cur_state, _ = rc_unpack(nrc.get_servo_state(fd, 0))
        if ok:
            print(f"  当前伺服状态: {cur_state}")
            if cur_state == 3:  # 已经是运行状态
                print("  ✅ 机械臂已经在运行状态")
                return True
        
        # 如果不是运行状态，尝试初始化
        print("机械臂不在运行状态，开始初始化...")

        # 切换到示教模式
        print("  切换到示教模式...")
        result = nrc.set_current_mode(fd, 0)
        if result != 0:
            print(f"  ❌ 切换示教模式失败: {result}")
            return False
        time.sleep(0.1)

        # 清除错误
        print("  清除错误...")
        nrc.clear_error(fd)

        # 如果不是就绪状态，设置就绪状态
        if cur_state != 1:
            print("  设置就绪状态...")
            result = nrc.set_servo_state(fd, 1)
            if result != 0:
                print(f"  ❌ 设置就绪状态失败: {result}")
                return False
            time.sleep(1.0)
        else:
            print("  机械臂已经是就绪状态，跳过设置")
        
        # 上电
        print("  机械臂上电...")
        result = nrc.set_servo_poweron(fd)
        if result != 0:
            print(f"  ❌ 上电失败: {result}")
            return False
        time.sleep(1.0)
        
        # 验证上电成功
        print("  验证上电状态...")
        ok, cur_state, _ = rc_unpack(nrc.get_servo_state(fd, 0))
        if ok and cur_state == 3:
            print("  ✅ 上电成功，机械臂进入运行状态")
        else:
            print(f"  ❌ 上电后状态不正确: {cur_state}")
            return False
        
        # 切换到运行模式
        print("  切换到运行模式...")
        result = nrc.set_current_mode(fd, 2)
        if result != 0:
            print(f"  ❌ 切换运行模式失败: {result}")
            return False
        
        # 清空队列并启用
        print("  清空运动队列...")
        nrc.queue_motion_clear_Data(fd)
        
        print("  启用运动队列...")
        result = nrc.queue_motion_set_status(fd, True)
        if result != 0:
            print(f"  ❌ 启用队列失败: {result}")
            return False
        
        # 设置用户坐标系
        print("  设置用户坐标系...")
        result = nrc.set_user_coord_number(fd, 1)
        if result != 0:
            print(f"  ❌ 设置用户坐标系失败: {result}")
            return False
        
        print("✅ 机械臂初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ 初始化异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_robot_shutdown(fd, nrc):
    """测试完整的机械臂下电流程（基于NRC文档标准流程）"""
    print(f"\n=== 测试完整下电流程 ===")

    try:
        # 检查当前状态
        print("检查当前机械臂状态...")
        ok, servo_state, _ = rc_unpack(nrc.get_servo_state(fd, 0))
        if ok:
            servo_states = {0: "停止", 1: "就绪", 2: "报警", 3: "运行"}
            state_name = servo_states.get(servo_state, f"未知({servo_state})")
            print(f"  当前伺服状态: {servo_state} ({state_name})")

        ok, running_state, _ = rc_unpack(nrc.get_robot_running_state(fd, 0))
        if ok:
            running_states = {0: "停止", 1: "暂停", 2: "运行"}
            state_name = running_states.get(running_state, f"未知({running_state})")
            print(f"  当前运行状态: {running_state} ({state_name})")

        # 步骤1: 停止任何正在进行的作业
        if running_state == 2:  # 如果正在运行
            print("  停止正在进行的作业...")
            try:
                nrc.job_stop(fd)
                time.sleep(1.0)
                print("  ✅ 作业已停止")
            except Exception as e:
                print(f"  ⚠️ 停止作业时出现异常: {e}")

        # 步骤2: 机器人下电（按照NRC文档标准流程）
        if servo_state == 3:  # 如果是运行状态，需要下电
            print("  机器人下电...")
            result = nrc.set_servo_poweroff(fd)
            if result == 0:
                print("  ✅ 机器人下电成功")
                time.sleep(1.0)

                # 验证下电状态
                ok, new_servo_state, _ = rc_unpack(nrc.get_servo_state(fd, 0))
                if ok:
                    servo_states = {0: "停止", 1: "就绪", 2: "报警", 3: "运行"}
                    state_name = servo_states.get(new_servo_state, f"未知({new_servo_state})")
                    print(f"  下电后伺服状态: {new_servo_state} ({state_name})")
                    if new_servo_state == 1:  # 应该变为就绪状态
                        print("  ✅ 下电状态验证成功")
                    else:
                        print(f"  ⚠️ 下电后状态异常: {new_servo_state}")
            else:
                print(f"  ❌ 机器人下电失败，错误码: {result}")
                return False
        else:
            print("  机器人已经不在运行状态，跳过下电")

        print("✅ 完整下电流程测试通过")
        return True

    except Exception as e:
        print(f"❌ 下电流程异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_nrc_controller_class():
    """测试NRC控制器类"""
    print(f"\n=== 测试NRC控制器类 ===")
    
    try:
        from nrc_controller import NRCController
        
        # 创建控制器实例
        class MockConfig:
            def __init__(self):
                self.params = {
                    'robot_ip': '************',
                    'robot_port': 6001,
                    'user_coord_number': 1,
                }
                
            def get_printer(self):
                class MockPrinter:
                    def add_object(self, name, obj):
                        pass
                    def register_event_handler(self, event, handler):
                        pass
                return MockPrinter()
                
            def get_name(self):
                return "nrc_controller"
                
            def get(self, name, default=None):
                return self.params.get(name, default)
                
            def getint(self, name, default=None):
                return int(self.params.get(name, default))
        
        config = MockConfig()
        controller = NRCController(config)
        
        print("测试连接方法...")
        success = controller.connect()
        if success:
            print("✅ 控制器连接成功")
            
            print("测试初始化方法...")
            init_success = controller.initialize()
            if init_success:
                print("✅ 控制器初始化成功")
                
                # 获取状态
                status = controller.get_status(0)
                print("控制器状态:")
                for key, value in status.items():
                    print(f"  {key}: {value}")
                
                # 断开连接
                controller.disconnect()
                print("✅ 控制器断开连接")
                return True
            else:
                print("❌ 控制器初始化失败")
                controller.disconnect()
                return False
        else:
            print("❌ 控制器连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 控制器类测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_motion_command(fd, nrc):
    """测试基础运动命令"""
    print(f"\n=== 测试基础运动命令 ===")
    
    try:
        # 检查队列状态
        print("检查队列状态...")
        ok, queue_len, _ = rc_unpack(nrc.queue_motion_get_queuelen(fd, 0))
        if ok:
            print(f"  当前队列长度: {queue_len}")
        
        # 这里可以添加简单的运动测试
        # 注意：实际运动测试需要确保机械臂在安全位置
        print("⚠️  基础运动命令测试需要确保机械臂安全")
        print("    建议在确认机械臂位置安全后再进行运动测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 运动命令测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("NRC机械臂硬件连接测试")
    print("=" * 50)
    
    # 获取机械臂IP（可以从命令行参数或配置文件读取）
    robot_ip = "************"
    robot_port = "6001"
    
    if len(sys.argv) > 1:
        robot_ip = sys.argv[1]
    if len(sys.argv) > 2:
        robot_port = sys.argv[2]
    
    print(f"目标机械臂: {robot_ip}:{robot_port}")
    print("=" * 50)
    
    # 执行基础测试
    passed = 0
    total = 2  # SDK导入 + 机械臂连接
    fd = None
    nrc = None

    # 测试SDK导入
    print(f"\n开始测试: NRC SDK导入")
    try:
        nrc_module = test_nrc_sdk_import()
        if nrc_module:
            passed += 1
            print(f"✅ NRC SDK导入 测试通过")
        else:
            print(f"❌ NRC SDK导入 测试失败")
    except Exception as e:
        print(f"❌ NRC SDK导入 测试异常: {e}")

    # 测试机械臂连接
    if nrc_module:
        print(f"\n开始测试: 机械臂连接")
        try:
            connection_result = test_robot_connection(robot_ip, robot_port)
            if connection_result:
                passed += 1
                print(f"✅ 机械臂连接 测试通过")
                fd, nrc = connection_result
            else:
                print(f"❌ 机械臂连接 测试失败")
        except Exception as e:
            print(f"❌ 机械臂连接 测试异常: {e}")
    
    # 如果连接成功，继续进行高级测试
    if fd and nrc:
        advanced_tests = [
            ("机械臂状态查询", lambda: test_robot_status(fd, nrc)),
            ("机械臂初始化", lambda: test_robot_initialization(fd, nrc)),
            ("基础运动命令", lambda: test_basic_motion_command(fd, nrc)),
            ("NRC控制器类", test_nrc_controller_class),
        ]
        
        for name, test_func in advanced_tests:
            print(f"\n开始测试: {name}")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {name} 测试通过")
                else:
                    print(f"❌ {name} 测试失败")
            except Exception as e:
                print(f"❌ {name} 测试异常: {e}")
        
        total += len(advanced_tests)
        
        # 完整的下电和断开流程
        try:
            print(f"\n开始测试: 完整下电流程")
            test_robot_shutdown(fd, nrc)
        except Exception as e:
            print(f"❌ 下电流程测试异常: {e}")

        # 断开连接
        try:
            nrc.disconnect_robot(fd)
            print("\n✅ 机械臂连接已断开")
        except:
            pass
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有硬件测试通过！")
        print("\n✅ 机械臂连接正常")
        print("✅ 状态查询正常") 
        print("✅ 初始化流程正常")
        print("✅ 控制器类工作正常")
        print("\n📋 下一步可以:")
        print("  1. 测试简单的运动命令")
        print("  2. 集成到完整的Klipper系统")
        print("  3. 开始Phase 2的G-code执行测试")
    else:
        print("⚠️  部分硬件测试失败")
        if not fd:
            print("\n🔧 连接问题排查:")
            print("  1. 检查机械臂IP地址")
            print("  2. 确认机械臂已开机")
            print("  3. 测试网络连通性: ping ************")
            print("  4. 检查防火墙设置")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
