#!/usr/bin/env python3
"""
NRC机械臂Klipper集成测试 - 使用实际G-code文件
测试我们的系统能否正确解析和处理jiyi.Gcode文件
"""

import sys
import os
import re
import math

# 添加Klipper路径
klippy_path = os.path.join(os.path.dirname(__file__), 'klippy')
sys.path.insert(0, klippy_path)
sys.path.insert(0, os.path.join(klippy_path, 'extras'))
sys.path.insert(0, os.path.join(klippy_path, 'kinematics'))

def parse_gcode_line(line):
    """解析G-code行，提取坐标和参数"""
    line = line.strip()
    if not line or line.startswith(';'):
        return None
        
    # 提取G-code命令
    cmd_match = re.match(r'^(G[01])', line)
    if not cmd_match:
        return None
        
    cmd = cmd_match.group(1)
    
    # 提取坐标参数
    coords = {}
    for param in ['X', 'Y', 'Z', 'A', 'B', 'C', 'E', 'F']:
        pattern = rf'{param}([-+]?\d*\.?\d+)'
        match = re.search(pattern, line)
        if match:
            coords[param] = float(match.group(1))
    
    return {
        'command': cmd,
        'coordinates': coords,
        'original_line': line
    }

def test_gcode_parsing():
    """测试G-code解析功能"""
    print("=== 测试G-code解析功能 ===")
    
    gcode_file = "mydir/jiyi.Gcode"
    if not os.path.exists(gcode_file):
        print(f"❌ G-code文件不存在: {gcode_file}")
        return False
    
    try:
        with open(gcode_file, 'r') as f:
            lines = f.readlines()
        
        parsed_commands = []
        for i, line in enumerate(lines, 1):
            parsed = parse_gcode_line(line)
            if parsed:
                parsed_commands.append((i, parsed))
        
        print(f"✅ 成功解析G-code文件")
        print(f"  总行数: {len(lines)}")
        print(f"  有效命令数: {len(parsed_commands)}")
        
        # 显示前几个解析结果
        print("  前5个解析命令:")
        for i, (line_num, cmd) in enumerate(parsed_commands[:5]):
            coords = cmd['coordinates']
            coord_str = ', '.join([f"{k}={v}" for k, v in coords.items()])
            print(f"    L{line_num}: {cmd['command']} {coord_str}")
        
        return parsed_commands
        
    except Exception as e:
        print(f"❌ G-code解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinate_conversion():
    """测试坐标转换功能"""
    print("\n=== 测试坐标转换功能 ===")
    
    try:
        from nrc_motion_planner import NRCMotionPlanner
        
        # 创建运动规划器实例
        class MockConfig:
            def __init__(self):
                self.params = {
                    'ds_pos': 10.0,
                    'da_deg': 2.0,
                    'v_lin_max': 60.0,
                    'v_ang_max': 45.0,
                    'a_eq_max': 300.0,
                    'a_lat_max': 800.0,
                    'gabc_to_rxyz': [180.0, 0.0, 0.0],
                    'g1_velocity_min_pct': 45,
                    'g1_velocity_max_pct': 60,
                    'g1_velocity_step_pct': 5,
                    'v_end_ratio': 0.05,
                    'eps_pos': 1e-4,
                    'eps_ang': 1e-3,
                }
                
            def get_printer(self):
                class MockPrinter:
                    def add_object(self, name, obj):
                        pass
                return MockPrinter()
                
            def get_name(self):
                return "test"
                
            def getfloat(self, name, default=None):
                return self.params.get(name, default)
                
            def getint(self, name, default=None):
                return int(self.params.get(name, default))
                
            def getfloatlist(self, name, default=None):
                return self.params.get(name, default)
        
        config = MockConfig()
        planner = NRCMotionPlanner(config)
        
        # 测试几个典型的坐标转换
        test_points = [
            (0.0, 0.0, 0.0),      # 原点
            (23.065, -15.888, 0.0),  # 第一个姿态
            (23.516, -9.009, 0.0),   # 第二个姿态
            (-27.759, 0.0, 0.0),     # 最大负角度
        ]
        
        print("ABC到RXYZ转换测试:")
        for a, b, c in test_points:
            rx, ry, rz = planner.gabc_to_rxyz(a, b, c)
            print(f"  ABC({a:7.3f}, {b:7.3f}, {c:7.3f}) -> RXYZ({rx:7.3f}, {ry:7.3f}, {rz:7.3f})")
        
        # 测试四元数转换
        print("\n四元数转换测试:")
        for a, b, c in test_points[:3]:
            rx, ry, rz = planner.gabc_to_rxyz(a, b, c)
            q = planner.euler_xyz_deg_to_quat(rx, ry, rz)
            back_euler = planner.quat_to_euler_xyz_deg(q)
            error = max(abs(back_euler[0]-rx), abs(back_euler[1]-ry), abs(back_euler[2]-rz))
            print(f"  RXYZ({rx:7.3f}, {ry:7.3f}, {rz:7.3f}) -> Q -> RXYZ 误差: {error:.6f}")
        
        print("✅ 坐标转换测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 坐标转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_path_planning_with_gcode():
    """使用实际G-code测试路径规划"""
    print("\n=== 使用实际G-code测试路径规划 ===")
    
    try:
        from nrc_motion_planner import NRCMotionPlanner
        
        # 创建运动规划器
        class MockConfig:
            def __init__(self):
                self.params = {
                    'ds_pos': 10.0,
                    'da_deg': 2.0,
                    'v_lin_max': 60.0,
                    'v_ang_max': 45.0,
                    'a_eq_max': 300.0,
                    'a_lat_max': 800.0,
                    'gabc_to_rxyz': [180.0, 0.0, 0.0],
                    'g1_velocity_min_pct': 45,
                    'g1_velocity_max_pct': 60,
                    'g1_velocity_step_pct': 5,
                    'v_end_ratio': 0.05,
                    'eps_pos': 1e-4,
                    'eps_ang': 1e-3,
                }
                
            def get_printer(self):
                class MockPrinter:
                    def add_object(self, name, obj):
                        pass
                return MockPrinter()
                
            def get_name(self):
                return "test"
                
            def getfloat(self, name, default=None):
                return self.params.get(name, default)
                
            def getint(self, name, default=None):
                return int(self.params.get(name, default))
                
            def getfloatlist(self, name, default=None):
                return self.params.get(name, default)
        
        config = MockConfig()
        planner = NRCMotionPlanner(config)
        
        # 解析G-code文件
        parsed_commands = test_gcode_parsing()
        if not parsed_commands:
            return False
        
        # 提取G1命令的路径点
        path_points = []
        for line_num, cmd in parsed_commands:
            if cmd['command'] == 'G1':
                coords = cmd['coordinates']
                if 'X' in coords and 'Y' in coords and 'Z' in coords:
                    x, y, z = coords['X'], coords['Y'], coords['Z']
                    a = coords.get('A', 0.0)
                    b = coords.get('B', 0.0) 
                    c = coords.get('C', 0.0)
                    
                    # 转换ABC到RXYZ
                    rx, ry, rz = planner.gabc_to_rxyz(a, b, c)
                    q = planner.euler_xyz_deg_to_quat(rx, ry, rz)
                    
                    path_points.append(((x, y, z), q))
        
        print(f"提取到 {len(path_points)} 个G1路径点")
        
        if len(path_points) < 2:
            print("❌ 路径点太少，无法进行路径规划测试")
            return False
        
        # 测试路径采样
        print("测试路径采样:")
        P = [point[0] for point in path_points[:5]]  # 取前5个点测试
        Q = [point[1] for point in path_points[:5]]
        
        sampled = planner.polyline_presample_block(P, Q)
        print(f"  原始点数: {len(P)}")
        print(f"  采样后点数: {len(sampled)}")
        
        # 计算路径统计
        total_distance = 0
        total_rotation = 0
        for i in range(len(P)-1):
            dist = planner.chord_len(P[i], P[i+1])
            rot = planner.angle_between_quats_deg(Q[i], Q[i+1])
            total_distance += dist
            total_rotation += rot
        
        print(f"  总路径长度: {total_distance:.2f} mm")
        print(f"  总旋转角度: {total_rotation:.2f} °")
        
        # 测试姿态展开
        unwrapped = planner.poses_quat_to_unwrapped_deg(sampled)
        print(f"  姿态展开后点数: {len(unwrapped)}")
        
        # 测试时间标定
        if len(unwrapped) >= 2:
            P_unwrapped = [(p[0], p[1], p[2]) for p in unwrapped]
            Q_unwrapped = [planner.euler_xyz_deg_to_quat(p[3], p[4], p[5]) for p in unwrapped]
            
            L, p_seg, merged = planner.time_scale_polyline(P_unwrapped, Q_unwrapped)
            print(f"  时间标定段数: {len(L)}")
            print(f"  速度段数: {len(p_seg)}")
            print(f"  合并后段数: {len(merged)}")
            
            if merged:
                print(f"  速度范围: {min(p_seg)}% - {max(p_seg)}%")
        
        print("✅ 路径规划测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 路径规划测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_nrc_controller_status():
    """测试NRC控制器状态"""
    print("\n=== 测试NRC控制器状态 ===")
    
    try:
        from nrc_controller import NRCController
        
        # 创建控制器实例
        class MockConfig:
            def __init__(self):
                self.params = {
                    'robot_ip': '************',
                    'robot_port': 6001,
                    'user_coord_number': 1,
                }
                
            def get_printer(self):
                class MockPrinter:
                    def add_object(self, name, obj):
                        pass
                    def register_event_handler(self, event, handler):
                        pass
                return MockPrinter()
                
            def get_name(self):
                return "nrc_controller"
                
            def get(self, name, default=None):
                return self.params.get(name, default)
                
            def getint(self, name, default=None):
                return int(self.params.get(name, default))
        
        config = MockConfig()
        controller = NRCController(config)
        
        # 测试状态获取
        status = controller.get_status(0)
        print("控制器状态:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        print("✅ NRC控制器状态测试通过")
        return True
        
    except Exception as e:
        print(f"❌ NRC控制器状态测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_kinematics_integration():
    """测试运动学集成"""
    print("\n=== 测试运动学集成 ===")
    
    try:
        from nrc_robot_arm import NRCRobotArmKinematics
        
        # 创建运动学实例
        class MockConfig:
            def __init__(self):
                self.params = {
                    'robot_ip': '************',
                    'robot_port': 6001,
                    'user_coord_number': 1,
                    'gabc_to_rxyz': [180.0, 0.0, 0.0],
                    'ds_pos': 10.0,
                    'da_deg': 2.0,
                    'v_lin_max': 60.0,
                    'v_ang_max': 45.0,
                    'a_eq_max': 300.0,
                    'a_lat_max': 800.0,
                    'g0_velocity_pct': 50,
                    'g1_velocity_min_pct': 45,
                    'g1_velocity_max_pct': 60,
                    'g1_velocity_step_pct': 5,
                    'accel_pct': 20,
                    'pl_smooth': 5,
                    'batch_send': 5,
                    'queue_high_watermark': 20,
                    'queue_low_watermark': 5,
                    'sync_output_pin': 1,
                }
                
            def get_printer(self):
                class MockPrinter:
                    def add_object(self, name, obj):
                        pass
                    def register_event_handler(self, event, handler):
                        pass
                    def lookup_object(self, name, default=None):
                        return default
                return MockPrinter()
                
            def get(self, name, default=None):
                return self.params.get(name, default)
                
            def getint(self, name, default=None):
                return int(self.params.get(name, default))
                
            def getfloat(self, name, default=None):
                return float(self.params.get(name, default))
                
            def getfloatlist(self, name, default=None):
                return self.params.get(name, default)
        
        class MockToolhead:
            def __init__(self):
                self.Coord = lambda x=0, y=0, z=0, e=0: [x, y, z, e]
        
        config = MockConfig()
        toolhead = MockToolhead()
        kinematics = NRCRobotArmKinematics(toolhead, config)
        
        # 测试基础接口
        steppers = kinematics.get_steppers()
        print(f"步进器数量: {len(steppers)}")
        
        position = kinematics.calc_position([])
        print(f"当前位置: {position}")
        
        status = kinematics.get_status(0)
        print("运动学状态:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        print("✅ 运动学集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 运动学集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("NRC机械臂Klipper集成测试 - 使用实际G-code")
    print("=" * 60)
    print(f"测试G-code文件: mydir/jiyi.Gcode")
    print("=" * 60)
    
    tests = [
        ("G-code解析", lambda: test_gcode_parsing() is not False),
        ("坐标转换", test_coordinate_conversion),
        ("路径规划", test_path_planning_with_gcode),
        ("NRC控制器", test_nrc_controller_status),
        ("运动学集成", test_kinematics_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n开始测试: {name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {name} 测试通过")
            else:
                print(f"❌ {name} 测试失败")
        except Exception as e:
            print(f"❌ {name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有集成测试通过！")
        print("\n✅ 系统已准备好处理实际G-code文件")
        print("✅ 数学函数库工作正常")
        print("✅ 路径规划算法正确")
        print("✅ 坐标转换精确")
        print("\n📋 下一步可以:")
        print("  1. 连接实际的NRC机械臂测试")
        print("  2. 开始Phase 2的完整G-code执行")
        print("  3. 集成到完整的Klipper系统")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
