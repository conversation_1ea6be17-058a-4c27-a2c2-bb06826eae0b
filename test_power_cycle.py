#!/usr/bin/env python3
"""
NRC机械臂完整上电-下电循环测试
验证标准的上电和下电流程
"""

import sys
import os
import time

# 添加NRC SDK路径
nrc_lib_path = os.path.join(os.path.dirname(__file__), 'lib')
if nrc_lib_path not in sys.path:
    sys.path.append(nrc_lib_path)

def rc_unpack(res):
    """解包SDK返回值"""
    if isinstance(res, (list, tuple)) and len(res) >= 1:
        code = res[0]
        data = res[1] if len(res) > 1 else None
        return (code == 0), data, code
    return False, None, None

def test_complete_power_cycle():
    """测试完整的上电-下电循环"""
    print("NRC机械臂完整上电-下电循环测试")
    print("=" * 50)
    
    try:
        from inexbot import nrc_interface as nrc
        print("✅ NRC SDK导入成功")
    except ImportError as e:
        print(f"❌ NRC SDK导入失败: {e}")
        return False
    
    robot_ip = "************"
    robot_port = "6001"
    fd = None
    
    try:
        # 步骤1: 连接机械臂
        print(f"\n🔗 步骤1: 连接机械臂 ({robot_ip}:{robot_port})")
        fd = nrc.connect_robot(robot_ip, robot_port)
        if fd <= 0:
            print(f"❌ 连接失败，返回值: {fd}")
            return False
        print(f"✅ 连接成功，文件描述符: {fd}")
        
        # 步骤2: 检查初始状态
        print(f"\n📊 步骤2: 检查初始状态")
        ok, servo_state, _ = rc_unpack(nrc.get_servo_state(fd, 0))
        if ok:
            servo_states = {0: "停止", 1: "就绪", 2: "报警", 3: "运行"}
            state_name = servo_states.get(servo_state, f"未知({servo_state})")
            print(f"  初始伺服状态: {servo_state} ({state_name})")
        
        ok, running_state, _ = rc_unpack(nrc.get_robot_running_state(fd, 0))
        if ok:
            running_states = {0: "停止", 1: "暂停", 2: "运行"}
            state_name = running_states.get(running_state, f"未知({running_state})")
            print(f"  初始运行状态: {running_state} ({state_name})")
        
        # 步骤3: 标准上电流程（基于NRC文档）
        print(f"\n🔋 步骤3: 标准上电流程")
        
        # 3.1 切换到示教模式
        print("  3.1 切换到示教模式...")
        result = nrc.set_current_mode(fd, 0)  # 0=示教模式
        if result != 0:
            print(f"  ❌ 切换示教模式失败: {result}")
            return False
        time.sleep(0.1)
        print("  ✅ 已切换到示教模式")
        
        # 3.2 清除错误
        print("  3.2 清除系统错误...")
        nrc.clear_error(fd)
        print("  ✅ 系统错误已清除")
        
        # 3.3 设置伺服为就绪状态（如果需要）
        if servo_state != 1:
            print("  3.3 设置伺服就绪状态...")
            result = nrc.set_servo_state(fd, 1)  # 1=就绪状态
            if result != 0:
                print(f"  ❌ 设置就绪状态失败: {result}")
                return False
            time.sleep(0.5)
            print("  ✅ 伺服已设置为就绪状态")
        else:
            print("  3.3 伺服已经是就绪状态，跳过设置")
        
        # 3.4 机器人上电
        print("  3.4 机器人上电...")
        result = nrc.set_servo_poweron(fd)
        if result != 0:
            print(f"  ❌ 机器人上电失败: {result}")
            return False
        time.sleep(2.0)  # 等待上电完成
        print("  ✅ 机器人上电成功")
        
        # 3.5 验证上电状态
        print("  3.5 验证上电状态...")
        ok, new_servo_state, _ = rc_unpack(nrc.get_servo_state(fd, 0))
        if ok:
            servo_states = {0: "停止", 1: "就绪", 2: "报警", 3: "运行"}
            state_name = servo_states.get(new_servo_state, f"未知({new_servo_state})")
            print(f"  上电后伺服状态: {new_servo_state} ({state_name})")
            if new_servo_state == 3:
                print("  ✅ 上电状态验证成功")
            else:
                print(f"  ❌ 上电状态异常: {new_servo_state}")
                return False
        
        # 3.6 切换到运行模式
        print("  3.6 切换到运行模式...")
        result = nrc.set_current_mode(fd, 2)  # 2=运行模式
        if result != 0:
            print(f"  ❌ 切换运行模式失败: {result}")
            return False
        print("  ✅ 已切换到运行模式")
        
        # 3.7 配置运动参数
        print("  3.7 配置运动参数...")
        nrc.queue_motion_clear_Data(fd)  # 清空队列
        result = nrc.queue_motion_set_status(fd, True)  # 启用队列
        if result != 0:
            print(f"  ❌ 启用队列失败: {result}")
            return False
        
        result = nrc.set_user_coord_number(fd, 1)  # 设置用户坐标系
        if result != 0:
            print(f"  ❌ 设置用户坐标系失败: {result}")
            return False
        print("  ✅ 运动参数配置完成")
        
        print("🎉 标准上电流程完成！")
        
        # 步骤4: 检查上电后状态（重新查询确保准确）
        print(f"\n📊 步骤4: 检查上电后状态")
        time.sleep(0.5)  # 等待状态稳定

        # 重新查询伺服状态
        ok, servo_state, _ = rc_unpack(nrc.get_servo_state(fd, 0))
        if ok:
            servo_states = {0: "停止", 1: "就绪", 2: "报警", 3: "运行"}
            state_name = servo_states.get(servo_state, f"未知({servo_state})")
            print(f"  当前伺服状态: {servo_state} ({state_name})")
        else:
            print("  ❌ 无法获取伺服状态")
            servo_state = -1

        ok, running_state, _ = rc_unpack(nrc.get_robot_running_state(fd, 0))
        if ok:
            running_states = {0: "停止", 1: "暂停", 2: "运行"}
            state_name = running_states.get(running_state, f"未知({running_state})")
            print(f"  当前运行状态: {running_state} ({state_name})")
        else:
            print("  ❌ 无法获取运行状态")
            running_state = -1

        ok, queue_len, _ = rc_unpack(nrc.queue_motion_get_queuelen(fd, 0))
        if ok:
            print(f"  当前队列长度: {queue_len}")
        else:
            print("  ❌ 无法获取队列长度")
        
        # 步骤5: 标准下电流程（基于NRC文档）
        print(f"\n🔌 步骤5: 标准下电流程")

        # 5.1 停止任何正在进行的作业
        if running_state == 2:  # 如果正在运行
            print("  5.1 停止正在进行的作业...")
            try:
                nrc.job_stop(fd)
                time.sleep(1.0)
                print("  ✅ 作业已停止")
            except Exception as e:
                print(f"  ⚠️ 停止作业时出现异常: {e}")
        else:
            print("  5.1 没有正在运行的作业，跳过停止")

        # 5.2 正确的下电流程（基于NRC状态机要求）
        print("  5.2 执行机器人下电...")
        print(f"    根据NRC状态机：下电需要在示教模式下进行")

        # 5.2.1 先切换到示教模式
        print("    5.2.1 切换到示教模式...")
        result = nrc.set_current_mode(fd, 0)  # 0=示教模式
        if result != 0:
            print(f"    ❌ 切换示教模式失败: {result}")
            return False
        time.sleep(0.1)
        print("    ✅ 已切换到示教模式")

        # 5.2.2 执行下电
        print("    5.2.2 执行下电命令...")
        result = nrc.set_servo_poweroff(fd)
        print(f"    下电命令返回值: {result}")

        if result == 0:
            time.sleep(1.0)
            print("  ✅ 下电命令执行成功")

            # 5.3 验证下电状态
            print("  5.3 验证下电状态...")
            ok, new_servo_state, _ = rc_unpack(nrc.get_servo_state(fd, 0))
            if ok:
                servo_states = {0: "停止", 1: "就绪", 2: "报警", 3: "运行"}
                state_name = servo_states.get(new_servo_state, f"未知({new_servo_state})")
                print(f"  下电后伺服状态: {new_servo_state} ({state_name})")
                if new_servo_state == 1:  # 应该变为就绪状态
                    print("  ✅ 下电状态验证成功")
                elif new_servo_state == 3:
                    print("  ❌ 下电失败，机械臂仍在运行状态")
                    return False
                else:
                    print(f"  ⚠️ 下电后状态异常: {new_servo_state}")
            else:
                print("  ❌ 无法获取下电后状态")
        else:
            print(f"  ❌ 机器人下电失败，错误码: {result}")
            print(f"    可能原因：状态机要求不满足或机械臂状态异常")
            return False
        
        print("🎉 标准下电流程完成！")
        
        # 步骤6: 断开连接
        print(f"\n🔗 步骤6: 断开连接")
        nrc.disconnect_robot(fd)
        print("✅ 连接已断开")
        
        print("\n" + "=" * 50)
        print("🎉 完整上电-下电循环测试成功！")
        print("✅ 所有流程都按照NRC文档标准执行")
        print("✅ 状态转换验证通过")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        
        # 异常情况下的安全清理
        if fd and fd > 0:
            try:
                print("\n🚨 执行紧急清理...")
                nrc.job_stop(fd)
                nrc.set_servo_poweroff(fd)
                nrc.disconnect_robot(fd)
                print("✅ 紧急清理完成")
            except:
                print("⚠️ 紧急清理失败，请手动检查机器人状态")
        
        return False

def main():
    """主函数"""
    success = test_complete_power_cycle()
    
    if success:
        print("\n🎯 测试结论:")
        print("  ✅ NRC机械臂硬件连接正常")
        print("  ✅ 标准上电流程工作正常")
        print("  ✅ 标准下电流程工作正常")
        print("  ✅ 状态转换逻辑正确")
        print("\n📋 下一步可以:")
        print("  1. 测试简单的运动命令")
        print("  2. 集成到Klipper系统")
        print("  3. 开始G-code执行测试")
    else:
        print("\n⚠️ 测试失败，请检查:")
        print("  1. 机械臂是否正常开机")
        print("  2. 网络连接是否稳定")
        print("  3. 机械臂是否处于安全位置")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
