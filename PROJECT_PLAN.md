# Klipper 6轴机械臂3D打印系统集成方案 (V2 - 完整版)

## 1. 项目概述

### 1.1. 目标
本项目旨在将一台6轴NRC工业机械臂与Klipper固件深度集成，实现一个高精度的6轴3D打印系统。我们将保留Klipper作为“大脑”的核心架构，负责G-code解析、运动规划和挤出控制，同时将机械臂作为“肌肉”，负责执行空间运动。

### 1.2. 核心挑战
根据对`nrc_interface.py` API的分析，机械臂控制器不提供任何关于“单段运动”或“批量运动”的**时间反馈**。这意味着Klipper在发出运动指令后，对机械臂的执行时机和耗时是“失明”的。这会导致Klipper的挤出机步进脉冲与机械臂的实际空间运动完全脱节，无法进行有效打印。

### 1.3. 解决方案：预测-批处理-指针同步 (PBPS) 模型
为解决此核心挑战，我们设计了**PBPS模型**。此模型不依赖于机械臂的时间反馈，而是通过“离线时间预测”和“在线指令指针同步”相结合的方式，重新建立Klipper对运动节奏的控制权。

*   **预测 (Predictive)**：在打印前，通过一个校准程序，测量并建立一个简单的“距离-速度-时间”预测模型。
*   **批处理 (Batch)**：在打印中，Klipper将一小批（例如50-100条）G-code移动指令打包发送给机械臂的运动队列。
*   **指针同步 (Pointer Synchronization)**：Klipper通过高频轮询（~20Hz）机械臂“当前正在执行的指令行号”，来精确追踪批处理任务的执行进度，并以此作为节拍器，驱动下一批指令的规划与发送。

## 2. 系统架构

为了简化系统耦合、最大化利用Klipper现有优势，我们采用“机械臂运动”与“打印头挤出”物理分离的增强型架构。

```mermaid
graph TD
    subgraph Klipper 主机 (Python, i7-13650HX)
        A[6轴 G-code 输入<br>(X,Y,Z,A,B,C,E)] --> B(Klipper 主程序 klippy.py);
        B --> C(Toolhead 工具头);
        C -- 目标位姿(Pose) --> D(运动学模块<br>/klippy/kinematics/robot_arm.py);
        C -- 挤出指令 --> H(标准 Klipper MCU 通信);
        D -- 关节角度 --> E(通信模块<br>/klippy/extras/nrc_robot.py);
        D -- 预测与同步 --> C;
    end

    subgraph 硬件
        F(NRC 机械臂控制器);
        G[物理机械臂];
        I(挤出机专用MCU<br>如 SKR Pico);
        J[挤出机电机/加热];
    end
    
    E -- nrc_interface.py (以太网) --> F;
    F -- 控制 --> G;
    H -- USB --> I;
    I -- 控制 --> J;
```
*   **架构优势**:
    1.  **彻底解耦**: 机械臂运动控制与挤出机步进脉冲生成完全分离。
    2.  **利用现有优势**: 可直接复用 Klipper 成熟、稳定、时序精确的步进脉冲生成和温度控制逻辑。
    3.  **简化 PBPS 模型**: PBPS 模型只需专注于运动与时间的同步，无需关心挤出细节。
*   **实施**: 在 `printer.cfg` 中配置两个 `[mcu]`，一个虚拟的用于机械臂，一个真实的用于挤出机。

## 3. 核心技术点：6轴运动学解算

`robot_arm.py` 的首要职责是成为一个**六轴逆运动学 (IK) 求解器**。

*   **输入**: Klipper 的 `toolhead` 模块传来一个包含**位置和姿态**的6D目标点 (X, Y, Z, A, B, C)。
*   **输出**: 机械臂6个关节的目标转动角度 (J1, J2, J3, J4, J5, J6)。
*   **实现建议**:
    *   **使用现有库**: 强烈建议在 `robot_arm.py` 中引入成熟的 Python 机器人学库，如 `ikpy`, `scipy.spatial.transform.Rotation`, `numpy`，以避免重复造轮子。
    *   **需要机械臂模型**: IK 解算需要精确的机械臂 DH 参数（连杆长度、关节偏移等），需在 `printer.cfg` 中配置，这些参数可能需要从 `get_robot_dh_param()` API 获取或从设备手册中查到。

## 4. 实施计划 (三阶段)

### **阶段一：打通基础通信与非同步运动**

**目标**：实现通过Klipper发送G-code，驱动机械臂完成关节空间和笛卡尔空间的移动。

**任务清单**:

1.  **创建 `klippy/extras/nrc_robot.py` (通信模块)**
    *   封装与 `nrc_interface.py` 的所有交互，包括连接、断开、API调用、状态查询和错误处理。重点封装 `queue_motion_push_back_moveL` 和 `job_get_current_line`。

2.  **创建 `klippy/kinematics/robot_arm.py` (运动学模块 - 骨架)**
    *   在 `__init__` 中，初始化 IK 求解器并加载机械臂 DH 参数。
    *   实现 `calc_position()` 方法，通过**正向运动学 (FK)** 将当前关节坐标转换为笛卡尔坐标。
    *   重写 `move()` 方法，在此阶段，该方法接收 Klipper 的位姿，并直接调用 `nrc_robot` 模块的 `moveL` 指令（或类似指令）进行单步、非同步的移动测试。
    *   实现 `check_move()` 方法，调用 IK 求解器检查目标位姿是否可解，作为基础安全检查。

3.  **配置 `printer.cfg`**
    *   添加 `[nrc_robot]` 和 `[robot_arm]` 部分，后者需包含机械臂的物理参数 (DH参数)。

--- 

### **阶段二：实现PBPS同步模型**

**目标**：实现运动与挤出的精确同步，能够打印出外形正确的模型。

**任务清单**:

1.  **创建离线时间预测校准工具**
    *   在 `robot_arm.py` 中注册一个新的G-code命令 `CALIBRATE_ARM_TIMING`。
    *   该命令自动执行一系列代表性移动，通过高频轮询精确测量每种移动的耗时，并将结果（距离、速度 -> 时间）保存到 `printer.cfg` 的 `[robot_arm_timing_model]` 部分。

2.  **重构 `robot_arm.py` 以实现PBPS模型**
    *   **引入内部移动队列** `self.move_batch`，`move()` 方法仅将移动指令存入此队列。
    *   **实现核心 `_flush_batch()` 方法**:
        1.  **时间/距离预测**: 遍历 `self.move_batch`，使用校准模型预测总时间 `T_estimated` 和总路径长 `D_total`。
        2.  **引导Toolhead**: 构造一个虚拟的、单一的 `Move` 对象，强制将其移动时间设为 `T_estimated`，移动距离设为 `D_total`。将此虚拟 `Move` 推入 `toolhead` 的队列，Klipper 的挤出机规划器便会为这个时间段规划出精确的挤出量。
        3.  **发送指令**: 将 `self.move_batch` 中的所有真实移动指令通过 `nrc_robot` 模块一次性推入机械臂的硬件队列并启动。
        4.  **指针轮询与等待**: 启动高频 timer 轮询 `job_get_current_line`，并使用 `toolhead.wait_moves()` 等待，直到批处理完成。

--- 

### **阶段三：坐标系、6D打印与易用性完善**

**目标**：实现真正的六轴联动打印，并使系统稳定、可靠。

**任务清单**:

1.  **定义 G-code 与 Slicer 工作流**
    *   **明确工具链**: 必须使用能够规划**刀具路径姿态**的 **CAM 软件** (如 `Fusion 360`, `SprutCAM`, `Rhino+Grasshopper`)。
    *   **开发后处理器**: 编写一个**后处理器 (Post-processor)** 脚本，将 CAM 软件的输出转换为 Klipper 能识别的、包含 A, B, C 轴姿态信息的 G-code (例如: `G1 X... Y... Z... A... B... C... E... F...`)。

2.  **`robot_arm.py` 支持 6D 运动**
    *   修改 `move()` 方法，使其能完整解析 G-code 中的 `A, B, C` 参数，并将其作为目标姿态传递给 IK 求解器。

3.  **实现完整的校准流程**
    *   创建 `BED_CALIBRATION` 宏，引导用户通过“三点法”校准打印床平面。
    *   创建 `TCP_CALIBRATION` 宏，引导用户完成工具中心点的标定流程。

4.  **增强安全边界检查**
    *   在 `check_move()` 中，除了检查笛卡尔坐标范围，还应检查 IK 解算出的**关节角度是否超出极限**。

## 5. 配置文件 (`printer.cfg`) 结构示例

```ini
[mcu]
# 主 Klipper MCU (如果需要的话)

[mcu extruder_mcu]
serial: /dev/serial/by-id/usb-Klipper_...

[nrc_robot]
ip_address: ************
port: 6001

[printer]
kinematics: robot_arm
max_velocity: 200
max_accel: 3000

[robot_arm]
# PBPS模型相关参数
batch_size: 50
# 机械臂物理参数 (DH, etc.)
link1_length: ...
# 机械臂安全工作区范围
max_x: 500
min_x: -500
# ... 其他轴的范围

# 此部分由 CALIBRATE_ARM_TIMING 自动生成和填充
[robot_arm_timing_model]
# model_data: {"(1, 100)": 0.01, "(5, 100)": 0.05, ...}
```

## 6. 总结

此方案在您原有优秀规划的基础上，通过引入“挤出机物理分离”简化了系统架构，并明确了“6轴逆运动学”和“CAM后处理”两个关键外部依赖。方案核心的 PBPS 模型逻辑清晰，分阶段的实施路径明确，为成功构建一个功能完善的六轴Klipper 3D打印系统提供了坚实的蓝图。
