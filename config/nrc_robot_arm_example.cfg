# NRC 6-axis Robot Arm Configuration Example
# 
# This configuration file shows how to set up a Klipper system with NRC robot arm
# Based on the parameters from test20250815.py

# ===== 基础打印机配置 =====
[printer]
kinematics: nrc_robot_arm
max_velocity: 300
max_accel: 3000
max_z_velocity: 25
max_z_accel: 300

# ===== NRC机械臂运动学配置 =====
[robot_arm]
# 机械臂连接参数
robot_ip: ************
robot_port: 6001
user_coord_number: 1

# GABC到RXYZ的转换偏移（来自test20250815.py）
gabc_to_rxyz: 180.0, 0.0, 0.0

# 运动参数（来自test20250815.py）
ds_pos: 10.0                # 位置采样间距 (mm)
da_deg: 2.0                 # 角度采样间距 (度)
v_lin_max: 60.0             # 最大线速度 (mm/s)
v_ang_max: 45.0             # 最大角速度 (度/s)
a_eq_max: 300.0             # 等效加速度 (mm/s²)
a_lat_max: 800.0            # 侧向加速度 (mm/s²)

# 速度参数（来自test20250815.py）
g0_velocity_pct: 50         # G0快速移动速度百分比
g1_velocity_min_pct: 45     # G1工作进给最小速度百分比
g1_velocity_max_pct: 60     # G1工作进给最大速度百分比
g1_velocity_step_pct: 5     # G1速度步进
accel_pct: 20               # 加速度百分比
pl_smooth: 5                # 平滑度等级

# 队列参数（来自test20250815.py，已验证的稳定参数）
batch_send: 5               # 批次发送大小
queue_high_watermark: 20    # 队列高水位
queue_low_watermark: 5      # 队列低水位

# 同步信号配置
sync_output_pin: 1          # 机械臂数字输出端口号

# ===== NRC控制器配置 =====
[nrc_controller]
robot_ip: ************
robot_port: 6001
user_coord_number: 1

# ===== NRC运动规划器配置 =====
[nrc_motion_planner]
# 运动参数（与robot_arm保持一致）
ds_pos: 10.0
da_deg: 2.0
v_lin_max: 60.0
v_ang_max: 45.0
a_eq_max: 300.0
a_lat_max: 800.0
v_end_ratio: 0.05
eps_pos: 1e-4
eps_ang: 1e-3

# GABC转换参数
gabc_to_rxyz: 180.0, 0.0, 0.0

# 速度参数
g1_velocity_min_pct: 45
g1_velocity_max_pct: 60
g1_velocity_step_pct: 5

# ===== 挤出机配置 =====
[extruder]
step_pin: ar54
dir_pin: ar55
enable_pin: !ar38
microsteps: 16
rotation_distance: 33.683
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: ar10
sensor_type: EPCOS 100K B57560G104F
sensor_pin: analog13
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

# 挤出机同步配置（用于硬件同步）
sync_pin: ar2               # 连接到机械臂同步信号的引脚
sync_mode: external         # 外部同步模式

# ===== 热床配置 =====
[heater_bed]
heater_pin: ar8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: analog14
control: watermark
min_temp: 0
max_temp: 130

# ===== MCU配置 =====
[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0

# ===== 显示和界面 =====
[display]
lcd_type: st7920
cs_pin: ar16
sclk_pin: ar23
sid_pin: ar17
encoder_pins: ^ar33, ^ar31
click_pin: ^!ar35

# ===== 虚拟SD卡 =====
[virtual_sdcard]
path: ~/gcode_files

# ===== 暂停/恢复功能 =====
[pause_resume]

# ===== 显示状态 =====
[display_status]

# ===== G-code宏示例 =====
[gcode_macro START_PRINT]
gcode:
    # 连接机械臂
    NRC_CONNECT
    # 设置温度
    M104 S{params.EXTRUDER_TEMP|default(200)}
    M140 S{params.BED_TEMP|default(60)}
    # 等待温度
    M109 S{params.EXTRUDER_TEMP|default(200)}
    M190 S{params.BED_TEMP|default(60)}
    # 机械臂归零
    G28
    # 开始打印
    G1 Z5 F3000

[gcode_macro END_PRINT]
gcode:
    # 停止加热
    M104 S0
    M140 S0
    # 机械臂移动到安全位置
    G1 Z20 F3000
    G1 X0 Y0 F6000
    # 断开机械臂连接
    NRC_DISCONNECT

[gcode_macro ROBOT_STATUS]
gcode:
    NRC_STATUS

# ===== 注意事项 =====
# 1. 请根据实际的机械臂IP地址修改robot_ip参数
# 2. 确保NRC SDK库文件在正确的路径下
# 3. 同步信号线需要正确连接机械臂DO端口和MCU引脚
# 4. 队列参数(5/20/5)已经过验证，建议不要随意修改
# 5. 速度和加速度参数需要根据实际机械臂性能调整
