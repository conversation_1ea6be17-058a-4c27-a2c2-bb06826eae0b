#!/usr/bin/env python3
"""
独立测试数学函数库
直接测试从test20250815.py移植的数学函数
"""

import math

# 直接复制数学函数进行测试
def deg2rad(d):
    return d * math.pi / 180.0

def rad2deg(r):
    return r * 180.0 / math.pi

def clamp(x, lo, hi):
    return max(lo, min(hi, x))

def normalize_angle_deg(a):
    a = (a + 180.0) % 360.0 - 180.0
    if a <= -180.0:
        a += 360.0
    return a

def unwrap_to_prev(cur, prev):
    """让cur加/减360后尽量靠近prev"""
    return cur + 360.0 * round((prev - cur) / 360.0)

def quat_normalize(q):
    w, x, y, z = q
    n = math.sqrt(w*w + x*x + y*y + z*z)
    return (1.0, 0.0, 0.0, 0.0) if n == 0 else (w/n, x/n, y/n, z/n)

def quat_dot(a, b):
    return a[0]*b[0] + a[1]*b[1] + a[2]*b[2] + a[3]*b[3]

def euler_xyz_deg_to_quat(rx, ry, rz):
    rx, ry, rz = deg2rad(rx), deg2rad(ry), deg2rad(rz)
    cx, sx = math.cos(rx/2), math.sin(rx/2)
    cy, sy = math.cos(ry/2), math.sin(ry/2)
    cz, sz = math.cos(rz/2), math.sin(rz/2)
    w = cz*cy*cx + sz*sy*sx
    x = cz*cy*sx - sz*sy*cx
    y = cz*sy*cx + sz*cy*sx
    z = sz*cy*cx - cz*sy*sx
    return quat_normalize((w, x, y, z))

def quat_to_euler_xyz_deg(q):
    w, x, y, z = q
    r11 = 1 - 2*(y*y + z*z)
    r21 = 2*(x*y + w*z)
    r31 = 2*(x*z - w*y)
    r32 = 2*(y*z + w*x)
    r33 = 1 - 2*(x*x + y*y)
    rx = math.atan2(r32, r33)
    ry = -math.asin(clamp(r31, -1.0, 1.0))
    rz = math.atan2(r21, r11)
    return (rad2deg(rx), rad2deg(ry), rad2deg(rz))

def quat_slerp(q0, q1, t):
    c = quat_dot(q0, q1)
    if c < 0.0:
        q1 = (-q1[0], -q1[1], -q1[2], -q1[3])
        c = -c
    if c > 0.9995:  # 线性近似
        w = q0[0] + t*(q1[0]-q0[0])
        x = q0[1] + t*(q1[1]-q0[1])
        y = q0[2] + t*(q1[2]-q0[2])
        z = q0[3] + t*(q1[3]-q0[3])
        return quat_normalize((w, x, y, z))
    th = math.acos(clamp(c, -1.0, 1.0))
    s = math.sin(th)
    s0 = math.sin((1-t)*th) / s
    s1 = math.sin(t*th) / s
    return (q0[0]*s0+q1[0]*s1, q0[1]*s0+q1[1]*s1, q0[2]*s0+q1[2]*s1, q0[3]*s0+q1[3]*s1)

def chord_len(p, q):
    dx, dy, dz = (q[0]-p[0], q[1]-p[1], q[2]-p[2])
    return math.sqrt(dx*dx + dy*dy + dz*dz)

def angle_between_quats_deg(q0, q1):
    c = clamp(abs(quat_dot(q0, q1)), -1.0, 1.0)
    return math.degrees(2.0 * math.acos(c))

def gabc_to_rxyz(a, b, c, offset=[180.0, 0.0, 0.0]):
    """GABC到RXYZ的转换"""
    rx = normalize_angle_deg(a + offset[0])
    ry = normalize_angle_deg(b + offset[1])
    rz = normalize_angle_deg(c + offset[2])
    return rx, ry, rz

def test_basic_math():
    """测试基础数学函数"""
    print("=== 测试基础数学函数 ===")
    
    # 测试角度转换
    print("角度转换测试:")
    print(f"  deg2rad(90) = {deg2rad(90):.6f} (期望: {math.pi/2:.6f})")
    print(f"  rad2deg(π/2) = {rad2deg(math.pi/2):.6f} (期望: 90.0)")
    
    # 测试角度归一化
    print("角度归一化测试:")
    print(f"  normalize_angle_deg(370) = {normalize_angle_deg(370)} (期望: 10)")
    print(f"  normalize_angle_deg(-190) = {normalize_angle_deg(-190)} (期望: 170)")
    
    # 测试clamp
    print("Clamp测试:")
    print(f"  clamp(5, 0, 10) = {clamp(5, 0, 10)} (期望: 5)")
    print(f"  clamp(-5, 0, 10) = {clamp(-5, 0, 10)} (期望: 0)")
    print(f"  clamp(15, 0, 10) = {clamp(15, 0, 10)} (期望: 10)")

def test_quaternion_functions():
    """测试四元数函数"""
    print("\n=== 测试四元数函数 ===")
    
    # 测试欧拉角到四元数转换
    print("欧拉角转四元数测试:")
    q = euler_xyz_deg_to_quat(90, 0, 0)
    print(f"  euler_xyz_deg_to_quat(90,0,0) = {q}")
    
    # 测试四元数到欧拉角转换
    euler = quat_to_euler_xyz_deg(q)
    print(f"  quat_to_euler_xyz_deg(q) = {euler}")
    print(f"  转换误差: {abs(euler[0] - 90):.6f}")
    
    # 测试四元数归一化
    q_unnorm = (2.0, 0.0, 0.0, 0.0)
    q_norm = quat_normalize(q_unnorm)
    print(f"  quat_normalize({q_unnorm}) = {q_norm}")
    
    # 测试SLERP插值
    print("SLERP插值测试:")
    q0 = euler_xyz_deg_to_quat(0, 0, 0)
    q1 = euler_xyz_deg_to_quat(90, 0, 0)
    q_mid = quat_slerp(q0, q1, 0.5)
    euler_mid = quat_to_euler_xyz_deg(q_mid)
    print(f"  SLERP(0°->90°, t=0.5) = {euler_mid}")
    print(f"  期望约45°，实际: {euler_mid[0]:.2f}°")

def test_geometry_functions():
    """测试几何函数"""
    print("\n=== 测试几何函数 ===")
    
    # 测试弦长计算
    p1 = (0, 0, 0)
    p2 = (3, 4, 0)
    length = chord_len(p1, p2)
    print(f"弦长测试:")
    print(f"  chord_len({p1}, {p2}) = {length:.2f} (期望: 5.0)")
    
    # 测试四元数角度
    q0 = euler_xyz_deg_to_quat(0, 0, 0)
    q1 = euler_xyz_deg_to_quat(90, 0, 0)
    angle = angle_between_quats_deg(q0, q1)
    print(f"四元数角度测试:")
    print(f"  angle_between_quats_deg(0°, 90°) = {angle:.2f}° (期望: 90.0)")

def test_gabc_conversion():
    """测试GABC转换"""
    print("\n=== 测试GABC转换 ===")
    
    # 测试GABC到RXYZ转换
    a, b, c = 10, 20, 30
    rx, ry, rz = gabc_to_rxyz(a, b, c)
    print(f"GABC转换测试:")
    print(f"  gabc_to_rxyz({a}, {b}, {c}) = ({rx}, {ry}, {rz})")
    print(f"  期望: ({a+180}, {b}, {c}) = (190, 20, 30)")

def test_path_sampling():
    """测试路径采样逻辑"""
    print("\n=== 测试路径采样逻辑 ===")
    
    # 模拟简单的路径采样
    P = [(0, 0, 0), (100, 0, 0)]  # 100mm直线
    Q = [euler_xyz_deg_to_quat(0, 0, 0), euler_xyz_deg_to_quat(0, 0, 90)]  # 90度旋转
    
    # 计算采样参数
    ds_pos = 10.0  # 位置采样间距
    da_deg = 2.0   # 角度采样间距
    
    L = chord_len(P[0], P[1])
    ang = angle_between_quats_deg(Q[0], Q[1])
    
    n_pos = max(1, int(math.ceil(L / ds_pos)))
    n_ang = max(1, int(math.ceil(ang / da_deg)))
    steps = max(n_pos, n_ang)
    
    print(f"路径采样测试:")
    print(f"  路径长度: {L}mm")
    print(f"  旋转角度: {ang}°")
    print(f"  位置采样数: {n_pos}")
    print(f"  角度采样数: {n_ang}")
    print(f"  最终采样数: {steps}")
    
    # 生成采样点
    sampled = []
    for k in range(steps + 1):
        t = k / steps if steps > 0 else 0
        x = P[0][0] + (P[1][0] - P[0][0]) * t
        y = P[0][1] + (P[1][1] - P[0][1]) * t
        z = P[0][2] + (P[1][2] - P[0][2]) * t
        q = quat_slerp(Q[0], Q[1], t)
        sampled.append((x, y, z, q))
    
    print(f"  生成采样点数: {len(sampled)}")
    print(f"  前3个点: {sampled[:3]}")

def main():
    """主测试函数"""
    print("NRC数学函数库独立测试")
    print("=" * 50)
    
    tests = [
        test_basic_math,
        test_quaternion_functions,
        test_geometry_functions,
        test_gabc_conversion,
        test_path_sampling,
    ]
    
    for test_func in tests:
        try:
            test_func()
            print("✅ 测试通过")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎉 数学函数库测试完成！")
    print("这些函数已成功移植到 klippy/extras/nrc_motion_planner.py")

if __name__ == "__main__":
    main()
