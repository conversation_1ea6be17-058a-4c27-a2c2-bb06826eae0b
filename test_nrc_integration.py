#!/usr/bin/env python3
"""
NRC Robot Arm Klipper Integration Test Script
测试基础集成功能

Usage: python3 test_nrc_integration.py
"""

import sys
import os
import math

# 添加Klipper路径
klippy_path = os.path.join(os.path.dirname(__file__), 'klippy')
sys.path.insert(0, klippy_path)
sys.path.insert(0, os.path.join(klippy_path, 'extras'))
sys.path.insert(0, os.path.join(klippy_path, 'kinematics'))

def test_math_functions():
    """测试数学函数库"""
    print("=== 测试数学函数库 ===")
    
    # 模拟导入运动规划器
    try:
        from nrc_motion_planner import NRCMotionPlanner
        
        # 创建一个模拟配置对象
        class MockConfig:
            def __init__(self):
                self.params = {
                    'ds_pos': 10.0,
                    'da_deg': 2.0,
                    'v_lin_max': 60.0,
                    'v_ang_max': 45.0,
                    'a_eq_max': 300.0,
                    'a_lat_max': 800.0,
                    'gabc_to_rxyz': [180.0, 0.0, 0.0],
                    'g1_velocity_min_pct': 45,
                    'g1_velocity_max_pct': 60,
                    'g1_velocity_step_pct': 5,
                }
                
            def get_printer(self):
                return MockPrinter()
                
            def get_name(self):
                return "nrc_motion_planner"
                
            def getfloat(self, name, default=None):
                return self.params.get(name, default)
                
            def getint(self, name, default=None):
                return int(self.params.get(name, default))
                
            def getfloatlist(self, name, default=None):
                return self.params.get(name, default)
        
        class MockPrinter:
            def add_object(self, name, obj):
                pass
        
        # 创建运动规划器实例
        config = MockConfig()
        planner = NRCMotionPlanner(config)
        
        # 测试基础数学函数
        print("测试角度转换:")
        print("  deg2rad(90) =", planner.deg2rad(90))
        print("  rad2deg(π/2) =", planner.rad2deg(math.pi/2))
        
        print("测试角度归一化:")
        print("  normalize_angle_deg(370) =", planner.normalize_angle_deg(370))
        print("  normalize_angle_deg(-190) =", planner.normalize_angle_deg(-190))
        
        print("测试四元数转换:")
        q = planner.euler_xyz_deg_to_quat(90, 0, 0)
        print("  euler_xyz_deg_to_quat(90,0,0) =", q)
        euler = planner.quat_to_euler_xyz_deg(q)
        print("  quat_to_euler_xyz_deg(q) =", euler)
        
        print("测试GABC转换:")
        rx, ry, rz = planner.gabc_to_rxyz(10, 20, 30)
        print("  gabc_to_rxyz(10,20,30) =", (rx, ry, rz))
        
        print("测试SLERP插值:")
        q0 = planner.euler_xyz_deg_to_quat(0, 0, 0)
        q1 = planner.euler_xyz_deg_to_quat(90, 0, 0)
        q_mid = planner.quat_slerp(q0, q1, 0.5)
        euler_mid = planner.quat_to_euler_xyz_deg(q_mid)
        print("  SLERP中点姿态 =", euler_mid)
        
        print("✅ 数学函数库测试通过")
        return True
        
    except Exception as e:
        print("❌ 数学函数库测试失败:", str(e))
        import traceback
        traceback.print_exc()
        return False

def test_path_planning():
    """测试路径规划算法"""
    print("\n=== 测试路径规划算法 ===")
    
    try:
        from nrc_motion_planner import NRCMotionPlanner
        
        # 使用上面的MockConfig
        class MockConfig:
            def __init__(self):
                self.params = {
                    'ds_pos': 10.0,
                    'da_deg': 2.0,
                    'v_lin_max': 60.0,
                    'v_ang_max': 45.0,
                    'a_eq_max': 300.0,
                    'a_lat_max': 800.0,
                    'gabc_to_rxyz': [180.0, 0.0, 0.0],
                    'g1_velocity_min_pct': 45,
                    'g1_velocity_max_pct': 60,
                    'g1_velocity_step_pct': 5,
                    'v_end_ratio': 0.05,
                    'eps_pos': 1e-4,
                    'eps_ang': 1e-3,
                }
                
            def get_printer(self):
                return MockPrinter()
                
            def get_name(self):
                return "nrc_motion_planner"
                
            def getfloat(self, name, default=None):
                return self.params.get(name, default)
                
            def getint(self, name, default=None):
                return int(self.params.get(name, default))
                
            def getfloatlist(self, name, default=None):
                return self.params.get(name, default)
        
        class MockPrinter:
            def add_object(self, name, obj):
                pass
        
        config = MockConfig()
        planner = NRCMotionPlanner(config)
        
        # 测试路径预采样
        print("测试路径预采样:")
        P = [(0, 0, 0), (100, 0, 0), (100, 100, 0)]  # 简单L形路径
        Q = [planner.euler_xyz_deg_to_quat(0, 0, 0),
             planner.euler_xyz_deg_to_quat(0, 0, 90),
             planner.euler_xyz_deg_to_quat(0, 0, 180)]
        
        sampled = planner.polyline_presample_block(P, Q)
        print("  原始路径点数:", len(P))
        print("  采样后点数:", len(sampled))
        print("  前3个采样点:", sampled[:3])
        
        # 测试姿态展开
        print("测试姿态展开:")
        unwrapped = planner.poses_quat_to_unwrapped_deg(sampled)
        print("  展开后点数:", len(unwrapped))
        if unwrapped:
            print("  前3个展开点:", unwrapped[:3])
        
        # 测试时间标定
        print("测试时间标定:")
        if len(unwrapped) >= 2:
            P_unwrapped = [(p[0], p[1], p[2]) for p in unwrapped]
            Q_unwrapped = [planner.euler_xyz_deg_to_quat(p[3], p[4], p[5]) for p in unwrapped]
            
            L, p_seg, merged = planner.time_scale_polyline(P_unwrapped, Q_unwrapped)
            print("  段长度:", L[:5] if len(L) > 5 else L)
            print("  段速度:", p_seg[:5] if len(p_seg) > 5 else p_seg)
            print("  合并段数:", len(merged))
        
        print("✅ 路径规划算法测试通过")
        return True
        
    except Exception as e:
        print("❌ 路径规划算法测试失败:", str(e))
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置文件加载"""
    print("\n=== 测试配置文件 ===")
    
    try:
        config_path = "config/nrc_robot_arm_example.cfg"
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                content = f.read()
                
            # 检查关键配置项
            required_sections = ['printer', 'robot_arm', 'nrc_controller', 'nrc_motion_planner']
            for section in required_sections:
                if f'[{section}]' in content:
                    print(f"  ✅ 找到配置段: {section}")
                else:
                    print(f"  ❌ 缺少配置段: {section}")
                    
            # 检查关键参数
            required_params = ['kinematics: nrc_robot_arm', 'robot_ip:', 'ds_pos:', 'batch_send:']
            for param in required_params:
                if param in content:
                    print(f"  ✅ 找到参数: {param}")
                else:
                    print(f"  ❌ 缺少参数: {param}")
                    
            print("✅ 配置文件测试通过")
            return True
        else:
            print("❌ 配置文件不存在:", config_path)
            return False
            
    except Exception as e:
        print("❌ 配置文件测试失败:", str(e))
        return False

def main():
    """主测试函数"""
    print("NRC Robot Arm Klipper Integration Test")
    print("=" * 50)
    
    tests = [
        ("数学函数库", test_math_functions),
        ("路径规划算法", test_path_planning),
        ("配置文件", test_config_loading),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！基础集成功能正常。")
        print("\n下一步:")
        print("1. 确保NRC SDK库文件在正确路径")
        print("2. 配置实际的机械臂IP地址")
        print("3. 运行 'NRC_CONNECT' 命令测试连接")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
