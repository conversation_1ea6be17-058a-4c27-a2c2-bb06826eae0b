#!/usr/bin/env python3
"""
测试Klipper能否正确加载我们的NRC模块
"""

import sys
import os
import tempfile

# 添加Klipper路径
klippy_path = os.path.join(os.path.dirname(__file__), 'klippy')
sys.path.insert(0, klippy_path)

def create_test_config():
    """创建测试配置文件"""
    config_content = """
# 最小化测试配置
[printer]
kinematics: nrc_robot_arm
max_velocity: 300
max_accel: 3000

[robot_arm]
robot_ip: ************
robot_port: 6001
ds_pos: 10.0
da_deg: 2.0
v_lin_max: 60.0

[nrc_controller]
robot_ip: ************
robot_port: 6001

[nrc_motion_planner]
ds_pos: 10.0
da_deg: 2.0
v_lin_max: 60.0

[mcu]
serial: /dev/null

[extruder]
step_pin: ar54
dir_pin: ar55
enable_pin: !ar38
microsteps: 16
rotation_distance: 33.683
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: ar10
sensor_type: EPCOS 100K B57560G104F
sensor_pin: analog13
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250
"""
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.cfg', delete=False) as f:
        f.write(config_content)
        return f.name

def test_module_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试运动学模块
        sys.path.insert(0, os.path.join(klippy_path, 'kinematics'))
        import nrc_robot_arm
        print("✅ nrc_robot_arm 模块导入成功")
        
        # 测试extras模块
        sys.path.insert(0, os.path.join(klippy_path, 'extras'))
        import nrc_controller
        print("✅ nrc_controller 模块导入成功")
        
        import nrc_motion_planner
        print("✅ nrc_motion_planner 模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_class_instantiation():
    """测试类实例化"""
    print("\n=== 测试类实例化 ===")
    
    try:
        # 模拟配置对象
        class MockConfig:
            def __init__(self, params):
                self.params = params
                
            def get_printer(self):
                return MockPrinter()
                
            def get_name(self):
                return "test"
                
            def get(self, name, default=None):
                return self.params.get(name, default)
                
            def getint(self, name, default=None):
                return int(self.params.get(name, default))
                
            def getfloat(self, name, default=None):
                return float(self.params.get(name, default))
                
            def getfloatlist(self, name, default=None):
                return self.params.get(name, default)
        
        class MockPrinter:
            def __init__(self):
                self.objects = {}
                
            def add_object(self, name, obj):
                self.objects[name] = obj
                
            def lookup_object(self, name, default=None):
                return self.objects.get(name, default)
                
            def register_event_handler(self, event, handler):
                pass
        
        class MockToolhead:
            def __init__(self):
                self.Coord = lambda x=0, y=0, z=0, e=0: [x, y, z, e]
        
        # 测试NRC控制器
        sys.path.insert(0, os.path.join(klippy_path, 'extras'))
        from nrc_controller import NRCController
        
        config = MockConfig({
            'robot_ip': '************',
            'robot_port': 6001,
            'user_coord_number': 1,
        })
        
        controller = NRCController(config)
        print("✅ NRCController 实例化成功")
        
        # 测试运动规划器
        from nrc_motion_planner import NRCMotionPlanner
        
        config = MockConfig({
            'ds_pos': 10.0,
            'da_deg': 2.0,
            'v_lin_max': 60.0,
            'v_ang_max': 45.0,
            'a_eq_max': 300.0,
            'a_lat_max': 800.0,
            'gabc_to_rxyz': [180.0, 0.0, 0.0],
            'g1_velocity_min_pct': 45,
            'g1_velocity_max_pct': 60,
            'g1_velocity_step_pct': 5,
            'v_end_ratio': 0.05,
            'eps_pos': 1e-4,
            'eps_ang': 1e-3,
        })
        
        planner = NRCMotionPlanner(config)
        print("✅ NRCMotionPlanner 实例化成功")
        
        # 测试运动学模块
        sys.path.insert(0, os.path.join(klippy_path, 'kinematics'))
        from nrc_robot_arm import NRCRobotArmKinematics
        
        config = MockConfig({
            'robot_ip': '************',
            'robot_port': 6001,
            'user_coord_number': 1,
            'gabc_to_rxyz': [180.0, 0.0, 0.0],
            'ds_pos': 10.0,
            'da_deg': 2.0,
            'v_lin_max': 60.0,
            'v_ang_max': 45.0,
            'a_eq_max': 300.0,
            'a_lat_max': 800.0,
            'g0_velocity_pct': 50,
            'g1_velocity_min_pct': 45,
            'g1_velocity_max_pct': 60,
            'g1_velocity_step_pct': 5,
            'accel_pct': 20,
            'pl_smooth': 5,
            'batch_send': 5,
            'queue_high_watermark': 20,
            'queue_low_watermark': 5,
            'sync_output_pin': 1,
        })
        
        toolhead = MockToolhead()
        kinematics = NRCRobotArmKinematics(toolhead, config)
        print("✅ NRCRobotArmKinematics 实例化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 类实例化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_function_calls():
    """测试函数调用"""
    print("\n=== 测试函数调用 ===")
    
    try:
        sys.path.insert(0, os.path.join(klippy_path, 'extras'))
        from nrc_motion_planner import NRCMotionPlanner
        
        # 创建运动规划器实例
        class MockConfig:
            def __init__(self):
                self.params = {
                    'ds_pos': 10.0,
                    'da_deg': 2.0,
                    'v_lin_max': 60.0,
                    'v_ang_max': 45.0,
                    'a_eq_max': 300.0,
                    'a_lat_max': 800.0,
                    'gabc_to_rxyz': [180.0, 0.0, 0.0],
                    'g1_velocity_min_pct': 45,
                    'g1_velocity_max_pct': 60,
                    'g1_velocity_step_pct': 5,
                    'v_end_ratio': 0.05,
                    'eps_pos': 1e-4,
                    'eps_ang': 1e-3,
                }
                
            def get_printer(self):
                class MockPrinter:
                    def add_object(self, name, obj):
                        pass
                return MockPrinter()
                
            def get_name(self):
                return "test"
                
            def getfloat(self, name, default=None):
                return self.params.get(name, default)
                
            def getint(self, name, default=None):
                return int(self.params.get(name, default))
                
            def getfloatlist(self, name, default=None):
                return self.params.get(name, default)
        
        config = MockConfig()
        planner = NRCMotionPlanner(config)
        
        # 测试数学函数
        result = planner.deg2rad(90)
        print(f"✅ deg2rad(90) = {result}")
        
        # 测试四元数转换
        q = planner.euler_xyz_deg_to_quat(90, 0, 0)
        print(f"✅ euler_xyz_deg_to_quat(90,0,0) = {q}")
        
        # 测试GABC转换
        rx, ry, rz = planner.gabc_to_rxyz(10, 20, 30)
        print(f"✅ gabc_to_rxyz(10,20,30) = ({rx}, {ry}, {rz})")
        
        # 测试路径采样
        P = [(0, 0, 0), (100, 0, 0)]
        Q = [planner.euler_xyz_deg_to_quat(0, 0, 0), planner.euler_xyz_deg_to_quat(0, 0, 90)]
        sampled = planner.polyline_presample_block(P, Q)
        print(f"✅ polyline_presample_block 生成 {len(sampled)} 个点")
        
        return True
        
    except Exception as e:
        print(f"❌ 函数调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("Klipper NRC模块加载测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_module_imports),
        ("类实例化", test_class_instantiation),
        ("函数调用", test_function_calls),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n开始测试: {name}")
        if test_func():
            passed += 1
            print(f"✅ {name} 测试通过")
        else:
            print(f"❌ {name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有模块加载测试通过！")
        print("\n✅ Phase 1 基础集成完成:")
        print("  - 目录结构创建完成")
        print("  - 数学函数库移植完成")
        print("  - 基础运动学类实现完成")
        print("  - 模块加载验证通过")
        print("\n📋 下一步 (T004-T008):")
        print("  - 实现运动学接口适配")
        print("  - 完善NRC控制器封装")
        print("  - 添加基础配置和测试")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
