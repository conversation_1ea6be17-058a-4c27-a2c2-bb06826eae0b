# NRC Robot Controller for Klipper
#
# <AUTHOR> <EMAIL>
#
# This file may be distributed under the terms of the GNU GPLv3 license.

import os
import sys
import time
import logging

# 添加NRC SDK路径（使用正确的路径）
nrc_lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if nrc_lib_path not in sys.path:
    sys.path.append(nrc_lib_path)

try:
    from inexbot import nrc_interface as nrc
    NRC_AVAILABLE = True
except ImportError as e:
    logging.warning("NRC interface not available: %s" % str(e))
    NRC_AVAILABLE = False

class NRCController:
    def __init__(self, config):
        self.printer = config.get_printer()
        self.name = config.get_name().split()[-1]
        
        # 配置参数
        self.ip = config.get('robot_ip', '************')
        self.port = config.getint('robot_port', 6001)
        self.user_coord_number = config.getint('user_coord_number', 1)
        
        # 连接状态
        self.fd = -1
        self.connected = False
        self.initialized = False
        
        # 注册到打印机对象
        self.printer.add_object('nrc_controller', self)
        
        # 延迟注册G-code命令（在ready事件中）
        self.printer.register_event_handler("klippy:ready", self._handle_ready)
        
        logging.info("NRC Controller initialized for %s:%d" % (self.ip, self.port))

    def _handle_ready(self):
        """Klipper ready后注册G-code命令"""
        try:
            gcode = self.printer.lookup_object('gcode')
            gcode.register_command('NRC_CONNECT', self.cmd_NRC_CONNECT,
                                  desc=self.cmd_NRC_CONNECT_help)
            gcode.register_command('NRC_DISCONNECT', self.cmd_NRC_DISCONNECT,
                                  desc=self.cmd_NRC_DISCONNECT_help)
            gcode.register_command('NRC_STATUS', self.cmd_NRC_STATUS,
                                  desc=self.cmd_NRC_STATUS_help)
            logging.info("NRC G-code commands registered")
        except Exception as e:
            logging.error("Failed to register NRC G-code commands: %s" % str(e))

    def connect(self):
        """连接到NRC机械臂控制器"""
        if not NRC_AVAILABLE:
            logging.error("NRC interface not available")
            return False
            
        if self.connected:
            return True
            
        try:
            logging.info("Connecting to NRC robot at %s:%s..." % (self.ip, self.port))
            
            # 使用你的连接逻辑（来自test20250815.py）
            try:
                from contextlib import redirect_stdout, redirect_stderr
                with open(os.devnull, "w") as devnull, redirect_stdout(devnull), redirect_stderr(devnull):
                    self.fd = nrc.connect_robot(self.ip, str(self.port))
            except Exception:
                self.fd = nrc.connect_robot(self.ip, str(self.port))
                
            self.connected = self.fd > 0
            
            if self.connected:
                logging.info("Connected to NRC robot successfully")
                return True
            else:
                logging.error("Failed to connect to NRC robot")
                return False
                
        except Exception as e:
            logging.error("Exception during NRC robot connection: %s" % str(e))
            return False

    def disconnect(self):
        """断开与NRC机械臂控制器的连接"""
        if self.connected:
            try:
                from contextlib import redirect_stdout, redirect_stderr
                with open(os.devnull, "w") as devnull, redirect_stdout(devnull), redirect_stderr(devnull):
                    nrc.disconnect_robot(self.fd)
            except Exception as e:
                logging.error("Exception during disconnect: %s" % str(e))
            finally:
                self.connected = False
                self.initialized = False
                self.fd = -1
                logging.info("Disconnected from NRC robot")

    def initialize(self):
        """初始化机械臂（来自你的test20250815.py逻辑）"""
        if not self.connected:
            return False
            
        try:
            # 检查并上电（如果需要）
            if not self._power_on_if_needed():
                return False
                
            # 切换到运行模式
            if nrc.set_current_mode(self.fd, 2) != 0:
                logging.error("Failed to set robot to run mode")
                return False
                
            # 清空队列并启用
            nrc.queue_motion_clear_Data(self.fd)
            if nrc.queue_motion_set_status(self.fd, True) != 0:
                logging.error("Failed to enable motion queue")
                return False
                
            # 设置用户坐标系
            if nrc.set_user_coord_number(self.fd, self.user_coord_number) != 0:
                logging.error("Failed to set user coordinate number")
                return False
                
            self.initialized = True
            logging.info("NRC robot initialized successfully")
            return True
            
        except Exception as e:
            logging.error("Exception during robot initialization: %s" % str(e))
            return False

    def _power_on_if_needed(self):
        """检查并上电（来自你的test20250815.py逻辑）"""
        try:
            # 检查当前伺服状态
            ok, cur, _ = self._rc_unpack(nrc.get_servo_state(self.fd, 0))
            if ok and cur == 3:  # 已经是运行状态
                return True
                
            # 切换到示教模式并上电
            nrc.set_current_mode(self.fd, 0)  # 示教模式
            time.sleep(0.1)
            nrc.clear_error(self.fd)
            nrc.set_servo_state(self.fd, 1)  # 就绪状态
            time.sleep(1.0)
            
            if nrc.set_servo_poweron(self.fd) != 0:
                return False
                
            time.sleep(1.0)
            
            # 验证上电成功
            ok, cur, _ = self._rc_unpack(nrc.get_servo_state(self.fd, 0))
            return ok and cur == 3
            
        except Exception as e:
            logging.error("Exception during power on: %s" % str(e))
            return False

    def _rc_unpack(self, res):
        """解包SDK返回值（来自你的test20250815.py）"""
        if isinstance(res, (list, tuple)) and len(res) >= 1:
            code = res[0]
            data = res[1] if len(res) > 1 else None
            return (code == 0), data, code
        return False, None, None

    def get_status(self, eventtime):
        """获取控制器状态"""
        status = {
            'connected': self.connected,
            'initialized': self.initialized,
            'ip': self.ip,
            'port': self.port,
        }
        
        if self.connected and NRC_AVAILABLE:
            try:
                # 获取伺服状态
                ok, servo_state, _ = self._rc_unpack(nrc.get_servo_state(self.fd, 0))
                if ok:
                    status['servo_state'] = servo_state
                    
                # 获取运行状态
                ok, running_state, _ = self._rc_unpack(nrc.get_robot_running_state(self.fd, 0))
                if ok:
                    status['running_state'] = running_state
                    
                # 获取队列长度
                ok, queue_len, _ = self._rc_unpack(nrc.queue_motion_get_queuelen(self.fd, 0))
                if ok:
                    status['queue_length'] = queue_len
                    
            except Exception as e:
                status['error'] = str(e)
                
        return status

    # G-code命令实现
    cmd_NRC_CONNECT_help = "Connect to NRC robot controller"
    def cmd_NRC_CONNECT(self, gcmd):
        if self.connect() and self.initialize():
            gcmd.respond_info("NRC robot connected and initialized successfully")
        else:
            gcmd.respond_error("Failed to connect or initialize NRC robot")

    cmd_NRC_DISCONNECT_help = "Disconnect from NRC robot controller"
    def cmd_NRC_DISCONNECT(self, gcmd):
        self.disconnect()
        gcmd.respond_info("NRC robot disconnected")

    cmd_NRC_STATUS_help = "Get NRC robot controller status"
    def cmd_NRC_STATUS(self, gcmd):
        status = self.get_status(0)
        for key, value in status.items():
            gcmd.respond_info("%s: %s" % (key, value))

def load_config(config):
    return NRCController(config)
