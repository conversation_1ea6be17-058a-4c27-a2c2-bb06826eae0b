# NRC Motion Planner for <PERSON>lip<PERSON>
# 
# This module will contain the motion planning algorithms from test20250815.py
#
# <AUTHOR> <EMAIL>
#
# This file may be distributed under the terms of the GNU GPLv3 license.

import math
import logging

class NRCMotionPlanner:
    def __init__(self, config):
        self.printer = config.get_printer()
        self.name = config.get_name().split()[-1]
        
        # 运动参数（来自你的test20250815.py）
        self.ds_pos = config.getfloat('ds_pos', 10.0)
        self.da_deg = config.getfloat('da_deg', 2.0)
        self.v_lin_max = config.getfloat('v_lin_max', 60.0)
        self.v_ang_max = config.getfloat('v_ang_max', 45.0)
        self.a_eq_max = config.getfloat('a_eq_max', 300.0)
        self.a_lat_max = config.getfloat('a_lat_max', 800.0)
        self.v_end_ratio = config.getfloat('v_end_ratio', 0.05)
        self.eps_pos = config.getfloat('eps_pos', 1e-4)
        self.eps_ang = config.getfloat('eps_ang', 1e-3)
        
        # GABC到RXYZ的转换偏移
        self.gabc_to_rxyz_offset = config.getfloatlist('gabc_to_rxyz', [180.0, 0.0, 0.0])
        
        # 速度参数
        self.g1_pct_min = config.getint('g1_velocity_min_pct', 45)
        self.g1_pct_max = config.getint('g1_velocity_max_pct', 60)
        self.g1_pct_step = config.getint('g1_velocity_step_pct', 5)
        
        # 注册到打印机对象
        self.printer.add_object('nrc_motion_planner', self)
        
        logging.info("NRC Motion Planner initialized")

    # ====== 数学函数库（来自你的test20250815.py）======
    
    def deg2rad(self, d):
        return d * math.pi / 180.0

    def rad2deg(self, r):
        return r * 180.0 / math.pi

    def clamp(self, x, lo, hi):
        return max(lo, min(hi, x))

    def normalize_angle_deg(self, a):
        a = (a + 180.0) % 360.0 - 180.0
        if a <= -180.0:
            a += 360.0
        return a

    def unwrap_to_prev(self, cur, prev):
        """让cur加/减360后尽量靠近prev"""
        return cur + 360.0 * round((prev - cur) / 360.0)

    def quat_normalize(self, q):
        w, x, y, z = q
        n = math.sqrt(w*w + x*x + y*y + z*z)
        return (1.0, 0.0, 0.0, 0.0) if n == 0 else (w/n, x/n, y/n, z/n)

    def quat_dot(self, a, b):
        return a[0]*b[0] + a[1]*b[1] + a[2]*b[2] + a[3]*b[3]

    def euler_xyz_deg_to_quat(self, rx, ry, rz):
        rx, ry, rz = self.deg2rad(rx), self.deg2rad(ry), self.deg2rad(rz)
        cx, sx = math.cos(rx/2), math.sin(rx/2)
        cy, sy = math.cos(ry/2), math.sin(ry/2)
        cz, sz = math.cos(rz/2), math.sin(rz/2)
        w = cz*cy*cx + sz*sy*sx
        x = cz*cy*sx - sz*sy*cx
        y = cz*sy*cx + sz*cy*sx
        z = sz*cy*cx - cz*sy*sx
        return self.quat_normalize((w, x, y, z))

    def quat_to_euler_xyz_deg(self, q):
        w, x, y, z = q
        r11 = 1 - 2*(y*y + z*z)
        r21 = 2*(x*y + w*z)
        r31 = 2*(x*z - w*y)
        r32 = 2*(y*z + w*x)
        r33 = 1 - 2*(x*x + y*y)
        rx = math.atan2(r32, r33)
        ry = -math.asin(self.clamp(r31, -1.0, 1.0))
        rz = math.atan2(r21, r11)
        return (self.rad2deg(rx), self.rad2deg(ry), self.rad2deg(rz))

    def quat_slerp(self, q0, q1, t):
        c = self.quat_dot(q0, q1)
        if c < 0.0:
            q1 = (-q1[0], -q1[1], -q1[2], -q1[3])
            c = -c
        if c > 0.9995:  # 线性近似
            w = q0[0] + t*(q1[0]-q0[0])
            x = q0[1] + t*(q1[1]-q0[1])
            y = q0[2] + t*(q1[2]-q0[2])
            z = q0[3] + t*(q1[3]-q0[3])
            return self.quat_normalize((w, x, y, z))
        th = math.acos(self.clamp(c, -1.0, 1.0))
        s = math.sin(th)
        s0 = math.sin((1-t)*th) / s
        s1 = math.sin(t*th) / s
        return (q0[0]*s0+q1[0]*s1, q0[1]*s0+q1[1]*s1, q0[2]*s0+q1[2]*s1, q0[3]*s0+q1[3]*s1)

    def chord_len(self, p, q):
        dx, dy, dz = (q[0]-p[0], q[1]-p[1], q[2]-p[2])
        return math.sqrt(dx*dx + dy*dy + dz*dz)

    def angle_between_quats_deg(self, q0, q1):
        c = self.clamp(abs(self.quat_dot(q0, q1)), -1.0, 1.0)
        return math.degrees(2.0 * math.acos(c))

    def discrete_curvature(self, p_prev, p_cur, p_next):
        v1 = (p_cur[0]-p_prev[0], p_cur[1]-p_prev[1], p_cur[2]-p_prev[2])
        v2 = (p_next[0]-p_cur[0], p_next[1]-p_cur[1], p_next[2]-p_cur[2])
        l1 = math.sqrt(v1[0]**2 + v1[1]**2 + v1[2]**2) + 1e-9
        l2 = math.sqrt(v2[0]**2 + v2[1]**2 + v2[2]**2) + 1e-9
        dot = self.clamp((v1[0]*v2[0] + v1[1]*v2[1] + v1[2]*v2[2])/(l1*l2), -1.0, 1.0)
        theta = math.acos(dot)
        s = 0.5*(l1 + l2)
        return 0.0 if s < 1e-9 else theta/s

    def gabc_to_rxyz(self, a, b, c):
        """GABC到RXYZ的转换（来自你的test20250815.py）"""
        rx = self.normalize_angle_deg(a + self.gabc_to_rxyz_offset[0])
        ry = self.normalize_angle_deg(b + self.gabc_to_rxyz_offset[1])
        rz = self.normalize_angle_deg(c + self.gabc_to_rxyz_offset[2])
        return rx, ry, rz

    # ====== 路径规划算法（完整移植自test20250815.py）======

    def polyline_presample_block(self, P, Q):
        """多段线预采样（完整移植自test20250815.py）"""
        out = [(P[0][0], P[0][1], P[0][2], Q[0])]
        for i in range(len(P)-1):
            p0, p1 = P[i], P[i+1]
            q0, q1 = Q[i], Q[i+1]
            L = self.chord_len(p0, p1)
            ang = self.angle_between_quats_deg(q0, q1)
            n_pos = max(1, int(math.ceil(L / self.ds_pos)))
            n_ang = max(1, int(math.ceil(ang / self.da_deg)))
            steps = max(n_pos, n_ang)
            for k in range(1, steps+1):
                t = k/steps
                x = p0[0] + (p1[0]-p0[0]) * t
                y = p0[1] + (p1[1]-p0[1]) * t
                z = p0[2] + (p1[2]-p0[2]) * t
                q = self.quat_slerp(q0, q1, t)
                out.append((x, y, z, q))
        return out

    def poses_quat_to_unwrapped_deg(self, poses_xyz_q):
        """姿态展开算法（完整移植自test20250815.py）"""
        out = []
        last_rx = last_ry = last_rz = None
        last_x = last_y = last_z = None

        for x, y, z, q in poses_xyz_q:
            rx, ry, rz = self.quat_to_euler_xyz_deg(q)
            if last_rx is None:
                rxu, ryu, rzu = rx, ry, rz
            else:
                rxu = self.unwrap_to_prev(rx, last_rx)
                ryu = self.unwrap_to_prev(ry, last_ry)
                rzu = self.unwrap_to_prev(rz, last_rz)

            if last_x is not None:
                dp = math.sqrt((x-last_x)**2 + (y-last_y)**2 + (z-last_z)**2)
                da = max(abs(rxu-last_rx), abs(ryu-last_ry), abs(rzu-last_rz))
                if dp < self.eps_pos and da < self.eps_ang:
                    continue

            out.append((x, y, z, rxu, ryu, rzu))
            last_rx, last_ry, last_rz = rxu, ryu, rzu
            last_x, last_y, last_z = x, y, z

        return out

    def time_scale_polyline(self, P, Q):
        """时间标定算法（完整移植自test20250815.py）"""
        n = len(P)
        if n < 2:
            return [0.0], [self.g1_pct_min], [(0, 0, self.g1_pct_min)]

        L = [self.chord_len(P[i], P[i+1]) for i in range(n-1)]
        TH = [self.angle_between_quats_deg(Q[i], Q[i+1]) for i in range(n-1)]

        # 计算曲率
        kappa = [0.0] * n
        for i in range(1, n-1):
            kappa[i] = self.discrete_curvature(P[i-1], P[i], P[i+1])

        # 曲率限速
        v_curve = []
        for i in range(n-1):
            k = max(kappa[i], kappa[i+1])
            v_curve.append(math.sqrt(self.a_lat_max/max(k, 1e-9)) if k > 1e-9 else float('inf'))

        # 线速度和角速度限制
        v_local = []
        for i in range(n-1):
            v_lin = self.v_lin_max
            v_ang = self.v_ang_max * (L[i]/max(TH[i], 1e-6))
            v_local.append(min(v_lin, v_ang, v_curve[i]))

        # 前向传播
        v_nodes = [0.0] * n
        vmax_all = max(v_local) if v_local else 0.0
        v_nodes[0] = self.v_end_ratio * vmax_all

        for i in range(1, n):
            v_allow = math.sqrt(max(0.0, v_nodes[i-1]*v_nodes[i-1] + 2*self.a_eq_max*L[i-1]))
            v_nodes[i] = min(v_allow, v_local[i-1])

        v_nodes[-1] = min(v_nodes[-1], self.v_end_ratio * vmax_all)

        # 后向传播
        for i in range(n-2, -1, -1):
            v_allow = math.sqrt(max(0.0, v_nodes[i+1]*v_nodes[i+1] + 2*self.a_eq_max*L[i]))
            v_nodes[i] = min(v_nodes[i], v_allow, v_local[i])

        # 段速度
        v_seg = [min(v_nodes[i], v_nodes[i+1], v_local[i]) for i in range(n-1)]
        v_peak = max(v_seg) if v_seg else 1.0
        if v_peak < 1e-9:
            v_peak = 1.0

        # 转换为百分比
        p_seg = []
        for v in v_seg:
            r = v / v_peak
            p = self.g1_pct_min + r * (self.g1_pct_max - self.g1_pct_min)
            p = int(round(p / self.g1_pct_step) * self.g1_pct_step)
            p = self.clamp(p, self.g1_pct_min, self.g1_pct_max)
            p_seg.append(p)

        # 合并相同速度的段
        merged = []
        start = 0
        cur = p_seg[0]
        for i in range(len(p_seg)):
            if p_seg[i] != cur:
                merged.append((start, i, cur))
                start = i
                cur = p_seg[i]
        merged.append((start, len(p_seg), cur))

        return L, p_seg, merged

    def get_status(self, eventtime):
        """获取运动规划器状态"""
        return {
            'ds_pos': self.ds_pos,
            'da_deg': self.da_deg,
            'v_lin_max': self.v_lin_max,
            'v_ang_max': self.v_ang_max,
            'a_eq_max': self.a_eq_max,
            'a_lat_max': self.a_lat_max,
        }

def load_config(config):
    return NRCMotionPlanner(config)
